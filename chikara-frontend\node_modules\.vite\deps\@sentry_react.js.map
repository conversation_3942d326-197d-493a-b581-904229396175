{"version": 3, "sources": ["../../../../node_modules/hoist-non-react-statics/node_modules/react-is/cjs/react-is.development.js", "../../../../node_modules/hoist-non-react-statics/node_modules/react-is/index.js", "../../../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js", "../../../../node_modules/@sentry/react/src/sdk.ts", "../../../../node_modules/@sentry/react/src/error.ts", "../../../../node_modules/@sentry/react/src/constants.ts", "../../../../node_modules/@sentry/react/src/hoist-non-react-statics.ts", "../../../../node_modules/@sentry/react/src/profiler.tsx", "../../../../node_modules/@sentry/react/src/debug-build.ts", "../../../../node_modules/@sentry/react/src/errorboundary.tsx", "../../../../node_modules/@sentry/react/src/redux.ts", "../../../../node_modules/@sentry/react/src/reactrouterv3.ts", "../../../../node_modules/@sentry/react/src/tanstackrouter.ts", "../../../../node_modules/@sentry/react/src/reactrouter.tsx", "../../../../node_modules/@sentry/react/src/reactrouterv6-compat-utils.tsx", "../../../../node_modules/@sentry/react/src/reactrouterv6.tsx", "../../../../node_modules/@sentry/react/src/reactrouterv7.tsx"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "'use strict';\n\nvar reactIs = require('react-is');\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nvar REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true\n};\nvar KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true\n};\nvar FORWARD_REF_STATICS = {\n  '$$typeof': true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true\n};\nvar MEMO_STATICS = {\n  '$$typeof': true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true\n};\nvar TYPE_STATICS = {};\nTYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;\nTYPE_STATICS[reactIs.Memo] = MEMO_STATICS;\n\nfunction getStatics(component) {\n  // React v16.11 and below\n  if (reactIs.isMemo(component)) {\n    return MEMO_STATICS;\n  } // React v16.12 and above\n\n\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;\n}\n\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n    if (objectPrototype) {\n      var inheritedComponent = getPrototypeOf(sourceComponent);\n\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);\n      }\n    }\n\n    var keys = getOwnPropertyNames(sourceComponent);\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n    }\n\n    var targetStatics = getStatics(targetComponent);\n    var sourceStatics = getStatics(sourceComponent);\n\n    for (var i = 0; i < keys.length; ++i) {\n      var key = keys[i];\n\n      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor);\n        } catch (e) {}\n      }\n    }\n  }\n\n  return targetComponent;\n}\n\nmodule.exports = hoistNonReactStatics;\n", "import type { BrowserOptions } from '@sentry/browser';\nimport { init as browserInit, setContext } from '@sentry/browser';\nimport type { Client } from '@sentry/core';\nimport { applySdkMetadata } from '@sentry/core';\nimport { version } from 'react';\n\n/**\n * Inits the React SDK\n */\nexport function init(options: BrowserOptions): Client | undefined {\n  const opts = {\n    ...options,\n  };\n\n  applySdkMetadata(opts, 'react');\n  setContext('react', { version });\n  return browserInit(opts);\n}\n", "import { captureException, withScope } from '@sentry/browser';\nimport { isError } from '@sentry/core';\nimport type { ErrorInfo } from 'react';\nimport { version } from 'react';\n\n/**\n * See if React major version is 17+ by parsing version string.\n */\nexport function isAtLeastReact17(reactVersion: string): boolean {\n  const reactMajor = reactVersion.match(/^([^.]+)/);\n  return reactMajor !== null && parseInt(reactMajor[0]) >= 17;\n}\n\n/**\n * Recurse through `error.cause` chain to set cause on an error.\n */\nexport function setCause(error: Error & { cause?: Error }, cause: Error): void {\n  const seenErrors = new WeakSet();\n\n  function recurse(error: Error & { cause?: Error }, cause: Error): void {\n    // If we've already seen the error, there is a recursive loop somewhere in the error's\n    // cause chain. Let's just bail out then to prevent a stack overflow.\n    if (seenErrors.has(error)) {\n      return;\n    }\n    if (error.cause) {\n      seenErrors.add(error);\n      return recurse(error.cause, cause);\n    }\n    error.cause = cause;\n  }\n\n  recurse(error, cause);\n}\n\n/**\n * Captures an error that was thrown by a React ErrorBoundary or React root.\n *\n * @param error The error to capture.\n * @param errorInfo The errorInfo provided by React.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nexport function captureReactException(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error: any,\n  { componentStack }: ErrorInfo,\n  hint?: Parameters<typeof captureException>[1],\n): string {\n  // If on React version >= 17, create stack trace from componentStack param and links\n  // to to the original error using `error.cause` otherwise relies on error param for stacktrace.\n  // Linking errors requires the `LinkedErrors` integration be enabled.\n  // See: https://reactjs.org/blog/2020/08/10/react-v17-rc.html#native-component-stacks\n  //\n  // Although `componentDidCatch` is typed to accept an `Error` object, it can also be invoked\n  // with non-error objects. This is why we need to check if the error is an error-like object.\n  // See: https://github.com/getsentry/sentry-javascript/issues/6167\n  if (isAtLeastReact17(version) && isError(error) && componentStack) {\n    const errorBoundaryError = new Error(error.message);\n    errorBoundaryError.name = `React ErrorBoundary ${error.name}`;\n    errorBoundaryError.stack = componentStack;\n\n    // Using the `LinkedErrors` integration to link the errors together.\n    setCause(error, errorBoundaryError);\n  }\n\n  return withScope(scope => {\n    scope.setContext('react', { componentStack });\n    return captureException(error, hint);\n  });\n}\n\n/**\n * Creates an error handler that can be used with the `onCaughtError`, `onUncaughtError`,\n * and `onRecoverableError` options in `createRoot` and `hydrateRoot` React DOM methods.\n *\n * @param callback An optional callback that will be called after the error is captured.\n * Use this to add custom handling for errors.\n *\n * @example\n *\n * ```JavaScript\n * const root = createRoot(container, {\n *  onCaughtError: Sentry.reactErrorHandler(),\n *  onUncaughtError: Sentry.reactErrorHandler((error, errorInfo) => {\n *    console.warn('Caught error', error, errorInfo.componentStack);\n *  });\n * });\n * ```\n */\nexport function reactErrorHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  callback?: (error: any, errorInfo: ErrorInfo, eventId: string) => void,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n): (error: any, errorInfo: ErrorInfo) => void {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (error: any, errorInfo: ErrorInfo) => {\n    const eventId = captureReactException(error, errorInfo);\n    if (callback) {\n      callback(error, errorInfo, eventId);\n    }\n  };\n}\n", "export const REACT_RENDER_OP = 'ui.react.render';\n\nexport const REACT_UPDATE_OP = 'ui.react.update';\n\nexport const REACT_MOUNT_OP = 'ui.react.mount';\n", "import * as hoistNonReactStaticsImport from 'hoist-non-react-statics';\n\n// Ensure we use the default export from hoist-non-react-statics if available,\n// falling back to the module itself. This handles both ESM and CJS usage.\nexport const hoistNonReactStatics = hoistNonReactStaticsImport.default || hoistNonReactStaticsImport;\n", "import { startInactiveSpan } from '@sentry/browser';\nimport type { Span } from '@sentry/core';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, spanToJSON, timestampInSeconds, withActiveSpan } from '@sentry/core';\nimport * as React from 'react';\nimport { REACT_MOUNT_OP, REACT_RENDER_OP, REACT_UPDATE_OP } from './constants';\nimport { hoistNonReactStatics } from './hoist-non-react-statics';\n\nexport const UNKNOWN_COMPONENT = 'unknown';\n\nexport type ProfilerProps = {\n  // The name of the component being profiled.\n  name: string;\n  // If the Profiler is disabled. False by default. This is useful if you want to disable profilers\n  // in certain environments.\n  disabled?: boolean;\n  // If time component is on page should be displayed as spans. True by default.\n  includeRender?: boolean;\n  // If component updates should be displayed as spans. True by default.\n  includeUpdates?: boolean;\n  // Component that is being profiled.\n  children?: React.ReactNode;\n  // props given to component being profiled.\n  updateProps: { [key: string]: unknown };\n};\n\n/**\n * The Profiler component leverages Sentry's Tracing integration to generate\n * spans based on component lifecycles.\n */\nclass Profiler extends React.Component<ProfilerProps> {\n  /**\n   * The span of the mount activity\n   * Made protected for the React Native SDK to access\n   */\n  protected _mountSpan: Span | undefined;\n  /**\n   * The span that represents the duration of time between shouldComponentUpdate and componentDidUpdate\n   */\n  protected _updateSpan: Span | undefined;\n\n  public constructor(props: ProfilerProps) {\n    super(props);\n    const { name, disabled = false } = this.props;\n\n    if (disabled) {\n      return;\n    }\n\n    this._mountSpan = startInactiveSpan({\n      name: `<${name}>`,\n      onlyIfParent: true,\n      op: REACT_MOUNT_OP,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.react.profiler',\n        'ui.component_name': name,\n      },\n    });\n  }\n\n  // If a component mounted, we can finish the mount activity.\n  public componentDidMount(): void {\n    if (this._mountSpan) {\n      this._mountSpan.end();\n    }\n  }\n\n  public shouldComponentUpdate({ updateProps, includeUpdates = true }: ProfilerProps): boolean {\n    // Only generate an update span if includeUpdates is true, if there is a valid mountSpan,\n    // and if the updateProps have changed. It is ok to not do a deep equality check here as it is expensive.\n    // We are just trying to give baseline clues for further investigation.\n    if (includeUpdates && this._mountSpan && updateProps !== this.props.updateProps) {\n      // See what props have changed between the previous props, and the current props. This is\n      // set as data on the span. We just store the prop keys as the values could be potentially very large.\n      const changedProps = Object.keys(updateProps).filter(k => updateProps[k] !== this.props.updateProps[k]);\n      if (changedProps.length > 0) {\n        const now = timestampInSeconds();\n        this._updateSpan = withActiveSpan(this._mountSpan, () => {\n          return startInactiveSpan({\n            name: `<${this.props.name}>`,\n            onlyIfParent: true,\n            op: REACT_UPDATE_OP,\n            startTime: now,\n            attributes: {\n              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.react.profiler',\n              'ui.component_name': this.props.name,\n              'ui.react.changed_props': changedProps,\n            },\n          });\n        });\n      }\n    }\n\n    return true;\n  }\n\n  public componentDidUpdate(): void {\n    if (this._updateSpan) {\n      this._updateSpan.end();\n      this._updateSpan = undefined;\n    }\n  }\n\n  // If a component is unmounted, we can say it is no longer on the screen.\n  // This means we can finish the span representing the component render.\n  public componentWillUnmount(): void {\n    const endTimestamp = timestampInSeconds();\n    const { name, includeRender = true } = this.props;\n\n    if (this._mountSpan && includeRender) {\n      const startTime = spanToJSON(this._mountSpan).timestamp;\n      withActiveSpan(this._mountSpan, () => {\n        const renderSpan = startInactiveSpan({\n          onlyIfParent: true,\n          name: `<${name}>`,\n          op: REACT_RENDER_OP,\n          startTime,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.react.profiler',\n            'ui.component_name': name,\n          },\n        });\n        if (renderSpan) {\n          // Have to cast to Span because the type of _mountSpan is Span | undefined\n          // and not getting narrowed properly\n          renderSpan.end(endTimestamp);\n        }\n      });\n    }\n  }\n\n  public render(): React.ReactNode {\n    return this.props.children;\n  }\n}\n\n// React.Component default props are defined as static property on the class\nObject.assign(Profiler, {\n  defaultProps: {\n    disabled: false,\n    includeRender: true,\n    includeUpdates: true,\n  },\n});\n\n/**\n * withProfiler is a higher order component that wraps a\n * component in a {@link Profiler} component. It is recommended that\n * the higher order component be used over the regular {@link Profiler} component.\n *\n * @param WrappedComponent component that is wrapped by Profiler\n * @param options the {@link ProfilerProps} you can pass into the Profiler\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction withProfiler<P extends Record<string, any>>(\n  WrappedComponent: React.ComponentType<P>,\n  // We do not want to have `updateProps` given in options, it is instead filled through the HOC.\n  options?: Pick<Partial<ProfilerProps>, Exclude<keyof ProfilerProps, 'updateProps' | 'children'>>,\n): React.FC<P> {\n  const componentDisplayName =\n    options?.name || WrappedComponent.displayName || WrappedComponent.name || UNKNOWN_COMPONENT;\n\n  const Wrapped: React.FC<P> = (props: P) => (\n    <Profiler {...options} name={componentDisplayName} updateProps={props}>\n      <WrappedComponent {...props} />\n    </Profiler>\n  );\n\n  Wrapped.displayName = `profiler(${componentDisplayName})`;\n\n  // Copy over static methods from Wrapped component to Profiler HOC\n  // See: https://reactjs.org/docs/higher-order-components.html#static-methods-must-be-copied-over\n  hoistNonReactStatics(Wrapped, WrappedComponent);\n  return Wrapped;\n}\n\n/**\n *\n * `useProfiler` is a React hook that profiles a React component.\n *\n * Requires React 16.8 or above.\n * @param name displayName of component being profiled\n */\nfunction useProfiler(\n  name: string,\n  options: { disabled?: boolean; hasRenderSpan?: boolean } = {\n    disabled: false,\n    hasRenderSpan: true,\n  },\n): void {\n  const [mountSpan] = React.useState(() => {\n    if (options?.disabled) {\n      return undefined;\n    }\n\n    return startInactiveSpan({\n      name: `<${name}>`,\n      onlyIfParent: true,\n      op: REACT_MOUNT_OP,\n      attributes: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.react.profiler',\n        'ui.component_name': name,\n      },\n    });\n  });\n\n  React.useEffect(() => {\n    if (mountSpan) {\n      mountSpan.end();\n    }\n\n    return (): void => {\n      if (mountSpan && options.hasRenderSpan) {\n        const startTime = spanToJSON(mountSpan).timestamp;\n        const endTimestamp = timestampInSeconds();\n\n        const renderSpan = startInactiveSpan({\n          name: `<${name}>`,\n          onlyIfParent: true,\n          op: REACT_RENDER_OP,\n          startTime,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.ui.react.profiler',\n            'ui.component_name': name,\n          },\n        });\n        if (renderSpan) {\n          // Have to cast to Span because the type of _mountSpan is Span | undefined\n          // and not getting narrowed properly\n          renderSpan.end(endTimestamp);\n        }\n      }\n    };\n    // We only want this to run once.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n}\n\nexport { Profiler, useProfiler, withProfiler };\n", "declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n", "import type { ReportDialogOptions } from '@sentry/browser';\nimport { getClient, showReportDialog, withScope } from '@sentry/browser';\nimport type { Scope } from '@sentry/core';\nimport { logger } from '@sentry/core';\nimport * as React from 'react';\nimport { DEBUG_BUILD } from './debug-build';\nimport { captureReactException } from './error';\nimport { hoistNonReactStatics } from './hoist-non-react-statics';\n\nexport const UNKNOWN_COMPONENT = 'unknown';\n\nexport type FallbackRender = (errorData: {\n  error: unknown;\n  componentStack: string;\n  eventId: string;\n  resetError(): void;\n}) => React.ReactElement;\n\ntype OnUnmountType = {\n  (error: null, componentStack: null, eventId: null): void;\n  (error: unknown, componentStack: string, eventId: string): void;\n};\n\nexport type ErrorBoundaryProps = {\n  children?: React.ReactNode | (() => React.ReactNode);\n  /** If a Sentry report dialog should be rendered on error */\n  showDialog?: boolean | undefined;\n  /**\n   * Options to be passed into the Sentry report dialog.\n   * No-op if {@link showDialog} is false.\n   */\n  dialogOptions?: ReportDialogOptions | undefined;\n  /**\n   * A fallback component that gets rendered when the error boundary encounters an error.\n   *\n   * Can either provide a React Component, or a function that returns React Component as\n   * a valid fallback prop. If a function is provided, the function will be called with\n   * the error, the component stack, and an function that resets the error boundary on error.\n   *\n   */\n  fallback?: React.ReactElement | FallbackRender | undefined;\n  /**\n   * If set to `true` or `false`, the error `handled` property will be set to the given value.\n   * If unset, the default behaviour is to rely on the presence of the `fallback` prop to determine\n   * if the error was handled or not.\n   */\n  handled?: boolean | undefined;\n  /** Called when the error boundary encounters an error */\n  onError?: ((error: unknown, componentStack: string, eventId: string) => void) | undefined;\n  /** Called on componentDidMount() */\n  onMount?: (() => void) | undefined;\n  /**\n   * Called when the error boundary resets due to a reset call from the\n   * fallback render props function.\n   */\n  onReset?: ((error: unknown, componentStack: string, eventId: string) => void) | undefined;\n  /**\n   * Called on componentWillUnmount() with the error, componentStack, and eventId.\n   *\n   * If the error boundary never encountered an error, the error\n   * componentStack, and eventId will be null.\n   */\n  onUnmount?: OnUnmountType | undefined;\n  /** Called before the error is captured by Sentry, allows for you to add tags or context using the scope */\n  beforeCapture?: ((scope: Scope, error: unknown, componentStack: string) => void) | undefined;\n};\n\ntype ErrorBoundaryState =\n  | {\n      componentStack: null;\n      error: null;\n      eventId: null;\n    }\n  | {\n      componentStack: React.ErrorInfo['componentStack'];\n      error: unknown;\n      eventId: string;\n    };\n\nconst INITIAL_STATE: ErrorBoundaryState = {\n  componentStack: null,\n  error: null,\n  eventId: null,\n};\n\n/**\n * A ErrorBoundary component that logs errors to Sentry.\n * NOTE: If you are a Sentry user, and you are seeing this stack frame, it means the\n * Sentry React SDK ErrorBoundary caught an error invoking your application code. This\n * is expected behavior and NOT indicative of a bug with the Sentry React SDK.\n */\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  public state: ErrorBoundaryState;\n\n  private readonly _openFallbackReportDialog: boolean;\n\n  private _lastEventId?: string;\n  private _cleanupHook?: () => void;\n\n  public constructor(props: ErrorBoundaryProps) {\n    super(props);\n\n    this.state = INITIAL_STATE;\n    this._openFallbackReportDialog = true;\n\n    const client = getClient();\n    if (client && props.showDialog) {\n      this._openFallbackReportDialog = false;\n      this._cleanupHook = client.on('afterSendEvent', event => {\n        if (!event.type && this._lastEventId && event.event_id === this._lastEventId) {\n          showReportDialog({ ...props.dialogOptions, eventId: this._lastEventId });\n        }\n      });\n    }\n  }\n\n  public componentDidCatch(error: unknown, errorInfo: React.ErrorInfo): void {\n    const { componentStack } = errorInfo;\n    const { beforeCapture, onError, showDialog, dialogOptions } = this.props;\n    withScope(scope => {\n      if (beforeCapture) {\n        beforeCapture(scope, error, componentStack);\n      }\n\n      const handled = this.props.handled != null ? this.props.handled : !!this.props.fallback;\n      const eventId = captureReactException(error, errorInfo, { mechanism: { handled } });\n\n      if (onError) {\n        onError(error, componentStack, eventId);\n      }\n      if (showDialog) {\n        this._lastEventId = eventId;\n        if (this._openFallbackReportDialog) {\n          showReportDialog({ ...dialogOptions, eventId });\n        }\n      }\n\n      // componentDidCatch is used over getDerivedStateFromError\n      // so that componentStack is accessible through state.\n      this.setState({ error, componentStack, eventId });\n    });\n  }\n\n  public componentDidMount(): void {\n    const { onMount } = this.props;\n    if (onMount) {\n      onMount();\n    }\n  }\n\n  public componentWillUnmount(): void {\n    const { error, componentStack, eventId } = this.state;\n    const { onUnmount } = this.props;\n    if (onUnmount) {\n      if (this.state === INITIAL_STATE) {\n        // If the error boundary never encountered an error, call onUnmount with null values\n        onUnmount(null, null, null);\n      } else {\n        // `componentStack` and `eventId` are guaranteed to be non-null here because `onUnmount` is only called\n        // when the error boundary has already encountered an error.\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        onUnmount(error, componentStack!, eventId!);\n      }\n    }\n\n    if (this._cleanupHook) {\n      this._cleanupHook();\n      this._cleanupHook = undefined;\n    }\n  }\n\n  public resetErrorBoundary(): void {\n    const { onReset } = this.props;\n    const { error, componentStack, eventId } = this.state;\n    if (onReset) {\n      // `componentStack` and `eventId` are guaranteed to be non-null here because `onReset` is only called\n      // when the error boundary has already encountered an error.\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      onReset(error, componentStack!, eventId!);\n    }\n    this.setState(INITIAL_STATE);\n  }\n\n  public render(): React.ReactNode {\n    const { fallback, children } = this.props;\n    const state = this.state;\n\n    // `componentStack` is only null in the initial state, when no error has been captured.\n    // If an error has been captured, `componentStack` will be a string.\n    // We cannot check `state.error` because null can be thrown as an error.\n    if (state.componentStack === null) {\n      return typeof children === 'function' ? children() : children;\n    }\n\n    const element =\n      typeof fallback === 'function'\n        ? React.createElement(fallback, {\n            error: state.error,\n            componentStack: state.componentStack,\n            resetError: () => this.resetErrorBoundary(),\n            eventId: state.eventId,\n          })\n        : fallback;\n\n    if (React.isValidElement(element)) {\n      return element;\n    }\n\n    if (fallback) {\n      DEBUG_BUILD && logger.warn('fallback did not produce a valid ReactElement');\n    }\n\n    // Fail gracefully if no fallback provided or is not valid\n    return null;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction withErrorBoundary<P extends Record<string, any>>(\n  WrappedComponent: React.ComponentType<P>,\n  errorBoundaryOptions: ErrorBoundaryProps,\n): React.FC<P> {\n  const componentDisplayName = WrappedComponent.displayName || WrappedComponent.name || UNKNOWN_COMPONENT;\n\n  const Wrapped: React.FC<P> = (props: P) => (\n    <ErrorBoundary {...errorBoundaryOptions}>\n      <WrappedComponent {...props} />\n    </ErrorBoundary>\n  );\n\n  Wrapped.displayName = `errorBoundary(${componentDisplayName})`;\n\n  // Copy over static methods from Wrapped component to Profiler HOC\n  // See: https://reactjs.org/docs/higher-order-components.html#static-methods-must-be-copied-over\n  hoistNonReactStatics(Wrapped, WrappedComponent);\n  return Wrapped;\n}\n\nexport { ErrorBoundary, withErrorBoundary };\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Scope } from '@sentry/core';\nimport { addBreadcrumb, addNonEnumerableProperty, getClient, getCurrentScope, getGlobalScope } from '@sentry/core';\n\ninterface Action<T = any> {\n  type: T;\n}\n\ninterface AnyAction extends Action {\n  [extraProps: string]: any;\n}\n\ntype Reducer<S = any, A extends Action = AnyAction> = (state: S | undefined, action: A) => S;\n\ntype Dispatch<A extends Action = AnyAction> = <T extends A>(action: T, ...extraArgs: any[]) => T;\n\ntype ExtendState<State, Extension> = [Extension] extends [never] ? State : State & Extension;\n\ntype Unsubscribe = () => void;\n\ninterface Store<S = any, A extends Action = AnyAction, StateExt = never, Ext = Record<string, unknown>> {\n  dispatch: Dispatch<A>;\n  getState(): S;\n  subscribe(listener: () => void): Unsubscribe;\n  replaceReducer<NewState, NewActions extends Action>(\n    nextReducer: Reducer<NewState, NewActions>,\n  ): Store<ExtendState<NewState, StateExt>, NewActions, StateExt, Ext> & Ext;\n}\n\ndeclare const $CombinedState: unique symbol;\n\ntype CombinedState<S> = { readonly [$CombinedState]?: undefined } & S;\n\ntype PreloadedState<S> = Required<S> extends {\n  [$CombinedState]: undefined;\n}\n  ? S extends CombinedState<infer S1>\n    ? { [K in keyof S1]?: S1[K] extends Record<string, unknown> ? PreloadedState<S1[K]> : S1[K] }\n    : never\n  : { [K in keyof S]: S[K] extends string | number | boolean | symbol ? S[K] : PreloadedState<S[K]> };\n\ntype StoreEnhancerStoreCreator<Ext = Record<string, unknown>, StateExt = never> = <\n  S = any,\n  A extends Action = AnyAction,\n>(\n  reducer: Reducer<S, A>,\n  preloadedState?: PreloadedState<S>,\n) => Store<ExtendState<S, StateExt>, A, StateExt, Ext> & Ext;\n\nexport interface SentryEnhancerOptions<S = any> {\n  /**\n   * Redux state in attachments or not.\n   * @default true\n   */\n  attachReduxState?: boolean;\n\n  /**\n   * Transforms the state before attaching it to an event.\n   * Use this to remove any private data before sending it to Sentry.\n   * Return null to not attach the state.\n   */\n  stateTransformer(state: S | undefined): (S & any) | null;\n  /**\n   * Transforms the action before sending it as a breadcrumb.\n   * Use this to remove any private data before sending it to Sentry.\n   * Return null to not send the breadcrumb.\n   */\n  actionTransformer(action: AnyAction): AnyAction | null;\n  /**\n   * Called on every state update, configure the Sentry Scope with the redux state.\n   */\n  configureScopeWithState?(scope: Scope, state: S): void;\n}\n\nconst ACTION_BREADCRUMB_CATEGORY = 'redux.action';\nconst ACTION_BREADCRUMB_TYPE = 'info';\n\nconst defaultOptions: SentryEnhancerOptions = {\n  attachReduxState: true,\n  actionTransformer: action => action,\n  stateTransformer: state => state || null,\n};\n\n/**\n * Creates an enhancer that would be passed to Redux's createStore to log actions and the latest state to Sentry.\n *\n * @param enhancerOptions Options to pass to the enhancer\n */\nfunction createReduxEnhancer(enhancerOptions?: Partial<SentryEnhancerOptions>): any {\n  // Note: We return an any type as to not have type conflicts.\n  const options = {\n    ...defaultOptions,\n    ...enhancerOptions,\n  };\n\n  return (next: StoreEnhancerStoreCreator): StoreEnhancerStoreCreator =>\n    <S = any, A extends Action = AnyAction>(reducer: Reducer<S, A>, initialState?: PreloadedState<S>) => {\n      options.attachReduxState &&\n        getGlobalScope().addEventProcessor((event, hint) => {\n          try {\n            // @ts-expect-error try catch to reduce bundle size\n            if (event.type === undefined && event.contexts.state.state.type === 'redux') {\n              hint.attachments = [\n                ...(hint.attachments || []),\n                // @ts-expect-error try catch to reduce bundle size\n                { filename: 'redux_state.json', data: JSON.stringify(event.contexts.state.state.value) },\n              ];\n            }\n          } catch (_) {\n            // empty\n          }\n          return event;\n        });\n\n      function sentryWrapReducer(reducer: Reducer<S, A>): Reducer<S, A> {\n        return (state, action): S => {\n          const newState = reducer(state, action);\n\n          const scope = getCurrentScope();\n\n          /* Action breadcrumbs */\n          const transformedAction = options.actionTransformer(action);\n          if (typeof transformedAction !== 'undefined' && transformedAction !== null) {\n            addBreadcrumb({\n              category: ACTION_BREADCRUMB_CATEGORY,\n              data: transformedAction,\n              type: ACTION_BREADCRUMB_TYPE,\n            });\n          }\n\n          /* Set latest state to scope */\n          const transformedState = options.stateTransformer(newState);\n          if (typeof transformedState !== 'undefined' && transformedState !== null) {\n            const client = getClient();\n            const options = client?.getOptions();\n            const normalizationDepth = options?.normalizeDepth || 3; // default state normalization depth to 3\n\n            // Set the normalization depth of the redux state to the configured `normalizeDepth` option or a sane number as a fallback\n            const newStateContext = { state: { type: 'redux', value: transformedState } };\n            addNonEnumerableProperty(\n              newStateContext,\n              '__sentry_override_normalization_depth__',\n              3 + // 3 layers for `state.value.transformedState`\n                normalizationDepth, // rest for the actual state\n            );\n\n            scope.setContext('state', newStateContext);\n          } else {\n            scope.setContext('state', null);\n          }\n\n          /* Allow user to configure scope with latest state */\n          const { configureScopeWithState } = options;\n          if (typeof configureScopeWithState === 'function') {\n            configureScopeWithState(scope, newState);\n          }\n\n          return newState;\n        };\n      }\n\n      const store = next(sentryWrapReducer(reducer), initialState);\n\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      store.replaceReducer = new Proxy(store.replaceReducer, {\n        apply: function (target, thisArg, args) {\n          target.apply(thisArg, [sentryWrapReducer(args[0])]);\n        },\n      });\n\n      return store;\n    };\n}\n\nexport { createReduxEnhancer };\n", "import {\n  browserTracingIntegration,\n  startBrowserTracingNavigationSpan,\n  startBrowserTracingPageLoadSpan,\n  WINDOW,\n} from '@sentry/browser';\nimport type { Integration, TransactionSource } from '@sentry/core';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '@sentry/core';\nimport type { Location } from './types';\n\n// Many of the types below had to be mocked out to prevent typescript issues\n// these types are required for correct functionality.\n\ntype HistoryV3 = {\n  location?: Location;\n  listen?(cb: (location: Location) => void): void;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n} & Record<string, any>;\n\nexport type Route = { path?: string; childRoutes?: Route[] };\n\nexport type Match = (\n  props: { location: Location; routes: Route[] },\n  cb: (error?: Error, _redirectLocation?: Location, renderProps?: { routes?: Route[] }) => void,\n) => void;\n\ntype ReactRouterV3TransactionSource = Extract<TransactionSource, 'url' | 'route'>;\n\ninterface ReactRouterOptions {\n  history: HistoryV3;\n  routes: Route[];\n  match: Match;\n}\n\n/**\n * A browser tracing integration that uses React Router v3 to instrument navigations.\n * Expects `history` (and optionally `routes` and `matchPath`) to be passed as options.\n */\nexport function reactRouterV3BrowserTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n): Integration {\n  const integration = browserTracingIntegration({\n    ...options,\n    instrumentPageLoad: false,\n    instrumentNavigation: false,\n  });\n\n  const { history, routes, match, instrumentPageLoad = true, instrumentNavigation = true } = options;\n\n  return {\n    ...integration,\n    afterAllSetup(client) {\n      integration.afterAllSetup(client);\n\n      if (instrumentPageLoad && WINDOW.location) {\n        normalizeTransactionName(\n          routes,\n          WINDOW.location as unknown as Location,\n          match,\n          (localName: string, source: ReactRouterV3TransactionSource = 'url') => {\n            startBrowserTracingPageLoadSpan(client, {\n              name: localName,\n              attributes: {\n                [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n                [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.pageload.react.reactrouter_v3',\n                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n              },\n            });\n          },\n        );\n      }\n\n      if (instrumentNavigation && history.listen) {\n        history.listen(location => {\n          if (location.action === 'PUSH' || location.action === 'POP') {\n            normalizeTransactionName(\n              routes,\n              location,\n              match,\n              (localName: string, source: TransactionSource = 'url') => {\n                startBrowserTracingNavigationSpan(client, {\n                  name: localName,\n                  attributes: {\n                    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n                    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.react.reactrouter_v3',\n                    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n                  },\n                });\n              },\n            );\n          }\n        });\n      }\n    },\n  };\n}\n\n/**\n * Normalize transaction names using `Router.match`\n */\nfunction normalizeTransactionName(\n  appRoutes: Route[],\n  location: Location,\n  match: Match,\n  callback: (pathname: string, source?: ReactRouterV3TransactionSource) => void,\n): void {\n  let name = location.pathname;\n  match(\n    {\n      location,\n      routes: appRoutes,\n    },\n    (error, _redirectLocation, renderProps) => {\n      if (error || !renderProps) {\n        return callback(name);\n      }\n\n      const routePath = getRouteStringFromRoutes(renderProps.routes || []);\n      if (routePath.length === 0 || routePath === '/*') {\n        return callback(name);\n      }\n\n      name = routePath;\n      return callback(name, 'route');\n    },\n  );\n}\n\n/**\n * Generate route name from array of routes\n */\nfunction getRouteStringFromRoutes(routes: Route[]): string {\n  if (!Array.isArray(routes) || routes.length === 0) {\n    return '';\n  }\n\n  const routesWithPaths: Route[] = routes.filter((route: Route) => !!route.path);\n\n  let index = -1;\n  for (let x = routesWithPaths.length - 1; x >= 0; x--) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const route = routesWithPaths[x]!;\n    if (route.path?.startsWith('/')) {\n      index = x;\n      break;\n    }\n  }\n\n  return routesWithPaths.slice(index).reduce((acc, { path }) => {\n    const pathSegment = acc === '/' || acc === '' ? path : `/${path}`;\n    return `${acc}${pathSegment}`;\n  }, '');\n}\n", "import {\n  browserTracingIntegration as originalBrowserTracingIntegration,\n  startBrowserTracingNavigationSpan,\n  startBrowserTracingPageLoadSpan,\n  WINDOW,\n} from '@sentry/browser';\nimport type { Integration } from '@sentry/core';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '@sentry/core';\nimport type { VendoredTanstackRouter, VendoredTanstackRouterRouteMatch } from './vendor/tanstackrouter-types';\n\n/**\n * A custom browser tracing integration for TanStack Router.\n *\n * The minimum compatible version of `@tanstack/react-router` is `1.64.0`.\n *\n * @param router A TanStack Router `Router` instance that should be used for routing instrumentation.\n * @param options Sentry browser tracing configuration.\n */\nexport function tanstackRouterBrowserTracingIntegration(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  router: any, // This is `any` because we don't want any type mismatches if TanStack Router changes their types\n  options: Parameters<typeof originalBrowserTracingIntegration>[0] = {},\n): Integration {\n  const castRouterInstance: VendoredTanstackRouter = router;\n\n  const browserTracingIntegrationInstance = originalBrowserTracingIntegration({\n    ...options,\n    instrumentNavigation: false,\n    instrumentPageLoad: false,\n  });\n\n  const { instrumentPageLoad = true, instrumentNavigation = true } = options;\n\n  return {\n    ...browserTracingIntegrationInstance,\n    afterAllSetup(client) {\n      browserTracingIntegrationInstance.afterAllSetup(client);\n\n      const initialWindowLocation = WINDOW.location;\n      if (instrumentPageLoad && initialWindowLocation) {\n        const matchedRoutes = castRouterInstance.matchRoutes(\n          initialWindowLocation.pathname,\n          castRouterInstance.options.parseSearch(initialWindowLocation.search),\n          { preload: false, throwOnError: false },\n        );\n\n        const lastMatch = matchedRoutes[matchedRoutes.length - 1];\n\n        startBrowserTracingPageLoadSpan(client, {\n          name: lastMatch ? lastMatch.routeId : initialWindowLocation.pathname,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.pageload.react.tanstack_router',\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: lastMatch ? 'route' : 'url',\n            ...routeMatchToParamSpanAttributes(lastMatch),\n          },\n        });\n      }\n\n      if (instrumentNavigation) {\n        // The onBeforeNavigate hook is called at the very beginning of a navigation and is only called once per navigation, even when the user is redirected\n        castRouterInstance.subscribe('onBeforeNavigate', onBeforeNavigateArgs => {\n          // onBeforeNavigate is called during pageloads. We can avoid creating navigation spans by comparing the states of the to and from arguments.\n          if (onBeforeNavigateArgs.toLocation.state === onBeforeNavigateArgs.fromLocation?.state) {\n            return;\n          }\n\n          const onResolvedMatchedRoutes = castRouterInstance.matchRoutes(\n            onBeforeNavigateArgs.toLocation.pathname,\n            onBeforeNavigateArgs.toLocation.search,\n            { preload: false, throwOnError: false },\n          );\n\n          const onBeforeNavigateLastMatch = onResolvedMatchedRoutes[onResolvedMatchedRoutes.length - 1];\n\n          const navigationLocation = WINDOW.location;\n          const navigationSpan = startBrowserTracingNavigationSpan(client, {\n            name: onBeforeNavigateLastMatch ? onBeforeNavigateLastMatch.routeId : navigationLocation.pathname,\n            attributes: {\n              [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.react.tanstack_router',\n              [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: onBeforeNavigateLastMatch ? 'route' : 'url',\n            },\n          });\n\n          // In case the user is redirected during navigation we want to update the span with the right value.\n          const unsubscribeOnResolved = castRouterInstance.subscribe('onResolved', onResolvedArgs => {\n            unsubscribeOnResolved();\n            if (navigationSpan) {\n              const onResolvedMatchedRoutes = castRouterInstance.matchRoutes(\n                onResolvedArgs.toLocation.pathname,\n                onResolvedArgs.toLocation.search,\n                { preload: false, throwOnError: false },\n              );\n\n              const onResolvedLastMatch = onResolvedMatchedRoutes[onResolvedMatchedRoutes.length - 1];\n\n              if (onResolvedLastMatch) {\n                navigationSpan.updateName(onResolvedLastMatch.routeId);\n                navigationSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'route');\n                navigationSpan.setAttributes(routeMatchToParamSpanAttributes(onResolvedLastMatch));\n              }\n            }\n          });\n        });\n      }\n    },\n  };\n}\n\nfunction routeMatchToParamSpanAttributes(match: VendoredTanstackRouterRouteMatch | undefined): Record<string, string> {\n  if (!match) {\n    return {};\n  }\n\n  const paramAttributes: Record<string, string> = {};\n  Object.entries(match.params).forEach(([key, value]) => {\n    paramAttributes[`url.path.params.${key}`] = value; // todo(v10): remove attribute which does not adhere to Sentry's semantic convention\n    paramAttributes[`url.path.parameter.${key}`] = value;\n    paramAttributes[`params.${key}`] = value; // params.[key] is an alias\n  });\n\n  return paramAttributes;\n}\n", "import {\n  browserTracingIntegration,\n  startBrowserTracingNavigationSpan,\n  startBrowserTracingPageLoadSpan,\n  WINDOW,\n} from '@sentry/browser';\nimport type { Client, Integration, Span, TransactionSource } from '@sentry/core';\nimport {\n  getActiveSpan,\n  getCurrentScope,\n  getRootSpan,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  spanToJSON,\n} from '@sentry/core';\nimport type { ReactElement } from 'react';\nimport * as React from 'react';\nimport { hoistNonReactStatics } from './hoist-non-react-statics';\nimport type { Action, Location } from './types';\n\n// We need to disable eslint no-explicit-any because any is required for the\n// react-router typings.\ntype Match = { path: string; url: string; params: Record<string, any>; isExact: boolean }; // eslint-disable-line @typescript-eslint/no-explicit-any\n\nexport type RouterHistory = {\n  location?: Location;\n  listen?(cb: (location: Location, action: Action) => void): void;\n} & Record<string, any>; // eslint-disable-line @typescript-eslint/no-explicit-any\n\nexport type RouteConfig = {\n  [propName: string]: unknown;\n  path?: string | string[];\n  exact?: boolean;\n  component?: ReactElement;\n  routes?: RouteConfig[];\n};\n\nexport type MatchPath = (pathname: string, props: string | string[] | any, parent?: Match | null) => Match | null; // eslint-disable-line @typescript-eslint/no-explicit-any\n\ninterface ReactRouterOptions {\n  history: RouterHistory;\n  routes?: RouteConfig[];\n  matchPath?: MatchPath;\n}\n\n/**\n * A browser tracing integration that uses React Router v4 to instrument navigations.\n * Expects `history` (and optionally `routes` and `matchPath`) to be passed as options.\n */\nexport function reactRouterV4BrowserTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n): Integration {\n  const integration = browserTracingIntegration({\n    ...options,\n    instrumentPageLoad: false,\n    instrumentNavigation: false,\n  });\n\n  const { history, routes, matchPath, instrumentPageLoad = true, instrumentNavigation = true } = options;\n\n  return {\n    ...integration,\n    afterAllSetup(client) {\n      integration.afterAllSetup(client);\n\n      instrumentReactRouter(\n        client,\n        instrumentPageLoad,\n        instrumentNavigation,\n        history,\n        'reactrouter_v4',\n        routes,\n        matchPath,\n      );\n    },\n  };\n}\n\n/**\n * A browser tracing integration that uses React Router v5 to instrument navigations.\n * Expects `history` (and optionally `routes` and `matchPath`) to be passed as options.\n */\nexport function reactRouterV5BrowserTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n): Integration {\n  const integration = browserTracingIntegration({\n    ...options,\n    instrumentPageLoad: false,\n    instrumentNavigation: false,\n  });\n\n  const { history, routes, matchPath, instrumentPageLoad = true, instrumentNavigation = true } = options;\n\n  return {\n    ...integration,\n    afterAllSetup(client) {\n      integration.afterAllSetup(client);\n\n      instrumentReactRouter(\n        client,\n        instrumentPageLoad,\n        instrumentNavigation,\n        history,\n        'reactrouter_v5',\n        routes,\n        matchPath,\n      );\n    },\n  };\n}\n\nfunction instrumentReactRouter(\n  client: Client,\n  instrumentPageLoad: boolean,\n  instrumentNavigation: boolean,\n  history: RouterHistory,\n  instrumentationName: string,\n  allRoutes: RouteConfig[] = [],\n  matchPath?: MatchPath,\n): void {\n  function getInitPathName(): string | undefined {\n    if (history.location) {\n      return history.location.pathname;\n    }\n\n    if (WINDOW.location) {\n      return WINDOW.location.pathname;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Normalizes a transaction name. Returns the new name as well as the\n   * source of the transaction.\n   *\n   * @param pathname The initial pathname we normalize\n   */\n  function normalizeTransactionName(pathname: string): [string, TransactionSource] {\n    if (allRoutes.length === 0 || !matchPath) {\n      return [pathname, 'url'];\n    }\n\n    const branches = matchRoutes(allRoutes, pathname, matchPath);\n    for (const branch of branches) {\n      if (branch.match.isExact) {\n        return [branch.match.path, 'route'];\n      }\n    }\n\n    return [pathname, 'url'];\n  }\n\n  if (instrumentPageLoad) {\n    const initPathName = getInitPathName();\n    if (initPathName) {\n      const [name, source] = normalizeTransactionName(initPathName);\n      startBrowserTracingPageLoadSpan(client, {\n        name,\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.pageload.react.${instrumentationName}`,\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n        },\n      });\n    }\n  }\n\n  if (instrumentNavigation && history.listen) {\n    history.listen((location, action) => {\n      if (action && (action === 'PUSH' || action === 'POP')) {\n        const [name, source] = normalizeTransactionName(location.pathname);\n        startBrowserTracingNavigationSpan(client, {\n          name,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.navigation.react.${instrumentationName}`,\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n          },\n        });\n      }\n    });\n  }\n}\n\n/**\n * Matches a set of routes to a pathname\n * Based on implementation from\n */\nfunction matchRoutes(\n  routes: RouteConfig[],\n  pathname: string,\n  matchPath: MatchPath,\n  branch: Array<{ route: RouteConfig; match: Match }> = [],\n): Array<{ route: RouteConfig; match: Match }> {\n  routes.some(route => {\n    const match = route.path\n      ? matchPath(pathname, route)\n      : branch.length\n        ? // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          branch[branch.length - 1]!.match // use parent match\n        : computeRootMatch(pathname); // use default \"root\" match\n\n    if (match) {\n      branch.push({ route, match });\n\n      if (route.routes) {\n        matchRoutes(route.routes, pathname, matchPath, branch);\n      }\n    }\n\n    return !!match;\n  });\n\n  return branch;\n}\n\nfunction computeRootMatch(pathname: string): Match {\n  return { path: '/', url: '/', params: {}, isExact: pathname === '/' };\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access */\nexport function withSentryRouting<P extends Record<string, any>, R extends React.ComponentType<P>>(Route: R): R {\n  const componentDisplayName = Route.displayName || Route.name;\n\n  const WrappedRoute: React.FC<P> = (props: P) => {\n    if (props?.computedMatch?.isExact) {\n      const route = props.computedMatch.path;\n      const activeRootSpan = getActiveRootSpan();\n\n      getCurrentScope().setTransactionName(route);\n\n      if (activeRootSpan) {\n        activeRootSpan.updateName(route);\n        activeRootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, 'route');\n      }\n    }\n\n    // @ts-expect-error Setting more specific React Component typing for `R` generic above\n    // will break advanced type inference done by react router params:\n    // https://github.com/DefinitelyTyped/DefinitelyTyped/blob/13dc4235c069e25fe7ee16e11f529d909f9f3ff8/types/react-router/index.d.ts#L154-L164\n    return <Route {...props} />;\n  };\n\n  WrappedRoute.displayName = `sentryRoute(${componentDisplayName})`;\n  hoistNonReactStatics(WrappedRoute, Route);\n  // @ts-expect-error Setting more specific React Component typing for `R` generic above\n  // will break advanced type inference done by react router params:\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/blob/13dc4235c069e25fe7ee16e11f529d909f9f3ff8/types/react-router/index.d.ts#L154-L164\n  return WrappedRoute;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access */\n\nfunction getActiveRootSpan(): Span | undefined {\n  const span = getActiveSpan();\n  const rootSpan = span && getRootSpan(span);\n\n  if (!rootSpan) {\n    return undefined;\n  }\n\n  const op = spanToJSON(rootSpan).op;\n\n  // Only use this root span if it is a pageload or navigation span\n  return op === 'navigation' || op === 'pageload' ? rootSpan : undefined;\n}\n", "/* eslint-disable max-lines */\n// Inspired from <PERSON><PERSON>'s solution:\n// https://gist.github.com/wontondon/e8c4bdf2888875e4c755712e99279536\n\nimport {\n  browserTracingIntegration,\n  startBrowserTracingNavigationSpan,\n  startBrowserTracingPageLoadSpan,\n  WINDOW,\n} from '@sentry/browser';\nimport type { Client, Integration, Span, TransactionSource } from '@sentry/core';\nimport {\n  getActiveSpan,\n  getClient,\n  getCurrentScope,\n  getRootSpan,\n  logger,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  spanToJSON,\n} from '@sentry/core';\nimport * as React from 'react';\nimport { DEBUG_BUILD } from './debug-build';\nimport { hoistNonReactStatics } from './hoist-non-react-statics';\nimport type {\n  Action,\n  AgnosticDataRouteMatch,\n  CreateRouterFunction,\n  CreateRoutesFromChildren,\n  Location,\n  MatchRoutes,\n  RouteMatch,\n  RouteObject,\n  Router,\n  RouterState,\n  UseEffect,\n  UseLocation,\n  UseNavigationType,\n  UseRoutes,\n} from './types';\n\nlet _useEffect: UseEffect;\nlet _useLocation: UseLocation;\nlet _useNavigationType: UseNavigationType;\nlet _createRoutesFromChildren: CreateRoutesFromChildren;\nlet _matchRoutes: MatchRoutes;\nlet _stripBasename: boolean = false;\n\nconst CLIENTS_WITH_INSTRUMENT_NAVIGATION = new WeakSet<Client>();\n\nexport interface ReactRouterOptions {\n  useEffect: UseEffect;\n  useLocation: UseLocation;\n  useNavigationType: UseNavigationType;\n  createRoutesFromChildren: CreateRoutesFromChildren;\n  matchRoutes: MatchRoutes;\n  stripBasename?: boolean;\n}\n\ntype V6CompatibleVersion = '6' | '7';\n\n// Keeping as a global variable for cross-usage in multiple functions\nconst allRoutes = new Set<RouteObject>();\n\n/**\n * Creates a wrapCreateBrowserRouter function that can be used with all React Router v6 compatible versions.\n */\nexport function createV6CompatibleWrapCreateBrowserRouter<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(\n  createRouterFunction: CreateRouterFunction<TState, TRouter>,\n  version: V6CompatibleVersion,\n): CreateRouterFunction<TState, TRouter> {\n  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `reactRouterV${version}Instrumentation was unable to wrap the \\`createRouter\\` function because of one or more missing parameters.`,\n      );\n\n    return createRouterFunction;\n  }\n\n  return function (routes: RouteObject[], opts?: Record<string, unknown> & { basename?: string }): TRouter {\n    addRoutesToAllRoutes(routes);\n\n    const router = createRouterFunction(routes, opts);\n    const basename = opts?.basename;\n\n    const activeRootSpan = getActiveRootSpan();\n\n    // The initial load ends when `createBrowserRouter` is called.\n    // This is the earliest convenient time to update the transaction name.\n    // Callbacks to `router.subscribe` are not called for the initial load.\n    if (router.state.historyAction === 'POP' && activeRootSpan) {\n      updatePageloadTransaction(\n        activeRootSpan,\n        router.state.location,\n        routes,\n        undefined,\n        basename,\n        Array.from(allRoutes),\n      );\n    }\n\n    router.subscribe((state: RouterState) => {\n      if (state.historyAction === 'PUSH' || state.historyAction === 'POP') {\n        // Wait for the next render if loading an unsettled route\n        if (state.navigation.state !== 'idle') {\n          requestAnimationFrame(() => {\n            handleNavigation({\n              location: state.location,\n              routes,\n              navigationType: state.historyAction,\n              version,\n              basename,\n              allRoutes: Array.from(allRoutes),\n            });\n          });\n        } else {\n          handleNavigation({\n            location: state.location,\n            routes,\n            navigationType: state.historyAction,\n            version,\n            basename,\n            allRoutes: Array.from(allRoutes),\n          });\n        }\n      }\n    });\n\n    return router;\n  };\n}\n\n/**\n * Creates a wrapCreateMemoryRouter function that can be used with all React Router v6 compatible versions.\n */\nexport function createV6CompatibleWrapCreateMemoryRouter<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(\n  createRouterFunction: CreateRouterFunction<TState, TRouter>,\n  version: V6CompatibleVersion,\n): CreateRouterFunction<TState, TRouter> {\n  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `reactRouterV${version}Instrumentation was unable to wrap the \\`createMemoryRouter\\` function because of one or more missing parameters.`,\n      );\n\n    return createRouterFunction;\n  }\n\n  return function (\n    routes: RouteObject[],\n    opts?: Record<string, unknown> & {\n      basename?: string;\n      initialEntries?: (string | { pathname: string })[];\n      initialIndex?: number;\n    },\n  ): TRouter {\n    addRoutesToAllRoutes(routes);\n\n    const router = createRouterFunction(routes, opts);\n    const basename = opts?.basename;\n\n    const activeRootSpan = getActiveRootSpan();\n    let initialEntry = undefined;\n\n    const initialEntries = opts?.initialEntries;\n    const initialIndex = opts?.initialIndex;\n\n    const hasOnlyOneInitialEntry = initialEntries && initialEntries.length === 1;\n    const hasIndexedEntry = initialIndex !== undefined && initialEntries && initialEntries[initialIndex];\n\n    initialEntry = hasOnlyOneInitialEntry\n      ? initialEntries[0]\n      : hasIndexedEntry\n        ? initialEntries[initialIndex]\n        : undefined;\n\n    const location = initialEntry\n      ? typeof initialEntry === 'string'\n        ? { pathname: initialEntry }\n        : initialEntry\n      : router.state.location;\n\n    if (router.state.historyAction === 'POP' && activeRootSpan) {\n      updatePageloadTransaction(activeRootSpan, location, routes, undefined, basename, Array.from(allRoutes));\n    }\n\n    router.subscribe((state: RouterState) => {\n      const location = state.location;\n      if (state.historyAction === 'PUSH' || state.historyAction === 'POP') {\n        handleNavigation({\n          location,\n          routes,\n          navigationType: state.historyAction,\n          version,\n          basename,\n          allRoutes: Array.from(allRoutes),\n        });\n      }\n    });\n\n    return router;\n  };\n}\n\n/**\n * Creates a browser tracing integration that can be used with all React Router v6 compatible versions.\n */\nexport function createReactRouterV6CompatibleTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n  version: V6CompatibleVersion,\n): Integration {\n  const integration = browserTracingIntegration({\n    ...options,\n    instrumentPageLoad: false,\n    instrumentNavigation: false,\n  });\n\n  const {\n    useEffect,\n    useLocation,\n    useNavigationType,\n    createRoutesFromChildren,\n    matchRoutes,\n    stripBasename,\n    instrumentPageLoad = true,\n    instrumentNavigation = true,\n  } = options;\n\n  return {\n    ...integration,\n    setup(client) {\n      integration.setup(client);\n\n      _useEffect = useEffect;\n      _useLocation = useLocation;\n      _useNavigationType = useNavigationType;\n      _matchRoutes = matchRoutes;\n      _createRoutesFromChildren = createRoutesFromChildren;\n      _stripBasename = stripBasename || false;\n    },\n    afterAllSetup(client) {\n      integration.afterAllSetup(client);\n\n      const initPathName = WINDOW.location?.pathname;\n      if (instrumentPageLoad && initPathName) {\n        startBrowserTracingPageLoadSpan(client, {\n          name: initPathName,\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'pageload',\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.pageload.react.reactrouter_v${version}`,\n          },\n        });\n      }\n\n      if (instrumentNavigation) {\n        CLIENTS_WITH_INSTRUMENT_NAVIGATION.add(client);\n      }\n    },\n  };\n}\n\nexport function createV6CompatibleWrapUseRoutes(origUseRoutes: UseRoutes, version: V6CompatibleVersion): UseRoutes {\n  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {\n    DEBUG_BUILD &&\n      logger.warn(\n        'reactRouterV6Instrumentation was unable to wrap `useRoutes` because of one or more missing parameters.',\n      );\n\n    return origUseRoutes;\n  }\n\n  const SentryRoutes: React.FC<{\n    children?: React.ReactNode;\n    routes: RouteObject[];\n    locationArg?: Partial<Location> | string;\n  }> = (props: { children?: React.ReactNode; routes: RouteObject[]; locationArg?: Partial<Location> | string }) => {\n    const isMountRenderPass = React.useRef(true);\n    const { routes, locationArg } = props;\n\n    const Routes = origUseRoutes(routes, locationArg);\n\n    const location = _useLocation();\n    const navigationType = _useNavigationType();\n\n    // A value with stable identity to either pick `locationArg` if available or `location` if not\n    const stableLocationParam =\n      typeof locationArg === 'string' || locationArg?.pathname ? (locationArg as { pathname: string }) : location;\n\n    _useEffect(() => {\n      const normalizedLocation =\n        typeof stableLocationParam === 'string' ? { pathname: stableLocationParam } : stableLocationParam;\n\n      if (isMountRenderPass.current) {\n        addRoutesToAllRoutes(routes);\n\n        updatePageloadTransaction(\n          getActiveRootSpan(),\n          normalizedLocation,\n          routes,\n          undefined,\n          undefined,\n          Array.from(allRoutes),\n        );\n        isMountRenderPass.current = false;\n      } else {\n        handleNavigation({\n          location: normalizedLocation,\n          routes,\n          navigationType,\n          version,\n          allRoutes: Array.from(allRoutes),\n        });\n      }\n    }, [navigationType, stableLocationParam]);\n\n    return Routes;\n  };\n\n  // eslint-disable-next-line react/display-name\n  return (routes: RouteObject[], locationArg?: Partial<Location> | string): React.ReactElement | null => {\n    return <SentryRoutes routes={routes} locationArg={locationArg} />;\n  };\n}\n\nexport function handleNavigation(opts: {\n  location: Location;\n  routes: RouteObject[];\n  navigationType: Action;\n  version: V6CompatibleVersion;\n  matches?: AgnosticDataRouteMatch;\n  basename?: string;\n  allRoutes?: RouteObject[];\n}): void {\n  const { location, routes, navigationType, version, matches, basename, allRoutes } = opts;\n  const branches = Array.isArray(matches) ? matches : _matchRoutes(routes, location, basename);\n\n  const client = getClient();\n  if (!client || !CLIENTS_WITH_INSTRUMENT_NAVIGATION.has(client)) {\n    return;\n  }\n\n  if ((navigationType === 'PUSH' || navigationType === 'POP') && branches) {\n    let name,\n      source: TransactionSource = 'url';\n    const isInDescendantRoute = locationIsInsideDescendantRoute(location, allRoutes || routes);\n\n    if (isInDescendantRoute) {\n      name = prefixWithSlash(rebuildRoutePathFromAllRoutes(allRoutes || routes, location));\n      source = 'route';\n    }\n\n    if (!isInDescendantRoute || !name) {\n      [name, source] = getNormalizedName(routes, location, branches, basename);\n    }\n\n    const activeSpan = getActiveSpan();\n    const isAlreadyInNavigationSpan = activeSpan && spanToJSON(activeSpan).op === 'navigation';\n\n    // Cross usage can result in multiple navigation spans being created without this check\n    if (isAlreadyInNavigationSpan) {\n      activeSpan?.updateName(name);\n      activeSpan?.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, source);\n    } else {\n      startBrowserTracingNavigationSpan(client, {\n        name,\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,\n          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'navigation',\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.navigation.react.reactrouter_v${version}`,\n        },\n      });\n    }\n  }\n}\n\n/**\n * Strip the basename from a pathname if exists.\n *\n * Vendored and modified from `react-router`\n * https://github.com/remix-run/react-router/blob/462bb712156a3f739d6139a0f14810b76b002df6/packages/router/utils.ts#L1038\n */\nfunction stripBasenameFromPathname(pathname: string, basename: string): string {\n  if (!basename || basename === '/') {\n    return pathname;\n  }\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return pathname;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  const startIndex = basename.endsWith('/') ? basename.length - 1 : basename.length;\n  const nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== '/') {\n    // pathname does not start with basename/\n    return pathname;\n  }\n\n  return pathname.slice(startIndex) || '/';\n}\n\nfunction sendIndexPath(pathBuilder: string, pathname: string, basename: string): [string, TransactionSource] {\n  const reconstructedPath = pathBuilder || _stripBasename ? stripBasenameFromPathname(pathname, basename) : pathname;\n\n  const formattedPath =\n    // If the path ends with a slash, remove it\n    reconstructedPath[reconstructedPath.length - 1] === '/'\n      ? reconstructedPath.slice(0, -1)\n      : // If the path ends with a wildcard, remove it\n        reconstructedPath.slice(-2) === '/*'\n        ? reconstructedPath.slice(0, -1)\n        : reconstructedPath;\n\n  return [formattedPath, 'route'];\n}\n\nfunction pathEndsWithWildcard(path: string): boolean {\n  return path.endsWith('*');\n}\n\nfunction pathIsWildcardAndHasChildren(path: string, branch: RouteMatch<string>): boolean {\n  return (pathEndsWithWildcard(path) && !!branch.route.children?.length) || false;\n}\n\nfunction routeIsDescendant(route: RouteObject): boolean {\n  return !!(!route.children && route.element && route.path?.endsWith('/*'));\n}\n\nfunction locationIsInsideDescendantRoute(location: Location, routes: RouteObject[]): boolean {\n  const matchedRoutes = _matchRoutes(routes, location) as RouteMatch[];\n\n  if (matchedRoutes) {\n    for (const match of matchedRoutes) {\n      if (routeIsDescendant(match.route) && pickSplat(match)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\nfunction addRoutesToAllRoutes(routes: RouteObject[]): void {\n  routes.forEach(route => {\n    const extractedChildRoutes = getChildRoutesRecursively(route);\n\n    extractedChildRoutes.forEach(r => {\n      allRoutes.add(r);\n    });\n  });\n}\n\nfunction getChildRoutesRecursively(route: RouteObject, allRoutes: Set<RouteObject> = new Set()): Set<RouteObject> {\n  if (!allRoutes.has(route)) {\n    allRoutes.add(route);\n\n    if (route.children && !route.index) {\n      route.children.forEach(child => {\n        const childRoutes = getChildRoutesRecursively(child, allRoutes);\n\n        childRoutes.forEach(r => {\n          allRoutes.add(r);\n        });\n      });\n    }\n  }\n\n  return allRoutes;\n}\n\nfunction pickPath(match: RouteMatch): string {\n  return trimWildcard(match.route.path || '');\n}\n\nfunction pickSplat(match: RouteMatch): string {\n  return match.params['*'] || '';\n}\n\nfunction trimWildcard(path: string): string {\n  return path[path.length - 1] === '*' ? path.slice(0, -1) : path;\n}\n\nfunction trimSlash(path: string): string {\n  return path[path.length - 1] === '/' ? path.slice(0, -1) : path;\n}\n\nfunction prefixWithSlash(path: string): string {\n  return path[0] === '/' ? path : `/${path}`;\n}\n\nfunction rebuildRoutePathFromAllRoutes(allRoutes: RouteObject[], location: Location): string {\n  const matchedRoutes = _matchRoutes(allRoutes, location) as RouteMatch[];\n\n  if (!matchedRoutes || matchedRoutes.length === 0) {\n    return '';\n  }\n\n  for (const match of matchedRoutes) {\n    if (match.route.path && match.route.path !== '*') {\n      const path = pickPath(match);\n      const strippedPath = stripBasenameFromPathname(location.pathname, prefixWithSlash(match.pathnameBase));\n\n      if (location.pathname === strippedPath) {\n        return trimSlash(strippedPath);\n      }\n\n      return trimSlash(\n        trimSlash(path || '') +\n          prefixWithSlash(\n            rebuildRoutePathFromAllRoutes(\n              allRoutes.filter(route => route !== match.route),\n              {\n                pathname: strippedPath,\n              },\n            ),\n          ),\n      );\n    }\n  }\n\n  return '';\n}\n\nfunction getNormalizedName(\n  routes: RouteObject[],\n  location: Location,\n  branches: RouteMatch[],\n  basename: string = '',\n): [string, TransactionSource] {\n  if (!routes || routes.length === 0) {\n    return [_stripBasename ? stripBasenameFromPathname(location.pathname, basename) : location.pathname, 'url'];\n  }\n\n  let pathBuilder = '';\n  if (branches) {\n    for (const branch of branches) {\n      const route = branch.route;\n      if (route) {\n        // Early return if index route\n        if (route.index) {\n          return sendIndexPath(pathBuilder, branch.pathname, basename);\n        }\n        const path = route.path;\n\n        // If path is not a wildcard and has no child routes, append the path\n        if (path && !pathIsWildcardAndHasChildren(path, branch)) {\n          const newPath = path[0] === '/' || pathBuilder[pathBuilder.length - 1] === '/' ? path : `/${path}`;\n          pathBuilder = trimSlash(pathBuilder) + prefixWithSlash(newPath);\n\n          // If the path matches the current location, return the path\n          if (trimSlash(location.pathname) === trimSlash(basename + branch.pathname)) {\n            if (\n              // If the route defined on the element is something like\n              // <Route path=\"/stores/:storeId/products/:productId\" element={<div>Product</div>} />\n              // We should check against the branch.pathname for the number of / separators\n              getNumberOfUrlSegments(pathBuilder) !== getNumberOfUrlSegments(branch.pathname) &&\n              // We should not count wildcard operators in the url segments calculation\n              !pathEndsWithWildcard(pathBuilder)\n            ) {\n              return [(_stripBasename ? '' : basename) + newPath, 'route'];\n            }\n\n            // if the last character of the pathbuilder is a wildcard and there are children, remove the wildcard\n            if (pathIsWildcardAndHasChildren(pathBuilder, branch)) {\n              pathBuilder = pathBuilder.slice(0, -1);\n            }\n\n            return [(_stripBasename ? '' : basename) + pathBuilder, 'route'];\n          }\n        }\n      }\n    }\n  }\n\n  const fallbackTransactionName = _stripBasename\n    ? stripBasenameFromPathname(location.pathname, basename)\n    : location.pathname || '/';\n\n  return [fallbackTransactionName, 'url'];\n}\n\nfunction updatePageloadTransaction(\n  activeRootSpan: Span | undefined,\n  location: Location,\n  routes: RouteObject[],\n  matches?: AgnosticDataRouteMatch,\n  basename?: string,\n  allRoutes?: RouteObject[],\n): void {\n  const branches = Array.isArray(matches)\n    ? matches\n    : (_matchRoutes(allRoutes || routes, location, basename) as unknown as RouteMatch[]);\n\n  if (branches) {\n    let name,\n      source: TransactionSource = 'url';\n\n    const isInDescendantRoute = locationIsInsideDescendantRoute(location, allRoutes || routes);\n\n    if (isInDescendantRoute) {\n      name = prefixWithSlash(rebuildRoutePathFromAllRoutes(allRoutes || routes, location));\n      source = 'route';\n    }\n\n    if (!isInDescendantRoute || !name) {\n      [name, source] = getNormalizedName(routes, location, branches, basename);\n    }\n\n    getCurrentScope().setTransactionName(name || '/');\n\n    if (activeRootSpan) {\n      activeRootSpan.updateName(name);\n      activeRootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, source);\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function createV6CompatibleWithSentryReactRouterRouting<P extends Record<string, any>, R extends React.FC<P>>(\n  Routes: R,\n  version: V6CompatibleVersion,\n): R {\n  if (!_useEffect || !_useLocation || !_useNavigationType || !_createRoutesFromChildren || !_matchRoutes) {\n    DEBUG_BUILD &&\n      logger.warn(`reactRouterV6Instrumentation was unable to wrap Routes because of one or more missing parameters.\n      useEffect: ${_useEffect}. useLocation: ${_useLocation}. useNavigationType: ${_useNavigationType}.\n      createRoutesFromChildren: ${_createRoutesFromChildren}. matchRoutes: ${_matchRoutes}.`);\n\n    return Routes;\n  }\n\n  const SentryRoutes: React.FC<P> = (props: P) => {\n    const isMountRenderPass = React.useRef(true);\n\n    const location = _useLocation();\n    const navigationType = _useNavigationType();\n\n    _useEffect(\n      () => {\n        const routes = _createRoutesFromChildren(props.children) as RouteObject[];\n\n        if (isMountRenderPass.current) {\n          addRoutesToAllRoutes(routes);\n\n          updatePageloadTransaction(getActiveRootSpan(), location, routes, undefined, undefined, Array.from(allRoutes));\n          isMountRenderPass.current = false;\n        } else {\n          handleNavigation({\n            location,\n            routes,\n            navigationType,\n            version,\n            allRoutes: Array.from(allRoutes),\n          });\n        }\n      },\n      // `props.children` is purposely not included in the dependency array, because we do not want to re-run this effect\n      // when the children change. We only want to start transactions when the location or navigation type change.\n      [location, navigationType],\n    );\n\n    // @ts-expect-error Setting more specific React Component typing for `R` generic above\n    // will break advanced type inference done by react router params\n    return <Routes {...props} />;\n  };\n\n  hoistNonReactStatics(SentryRoutes, Routes);\n\n  // @ts-expect-error Setting more specific React Component typing for `R` generic above\n  // will break advanced type inference done by react router params\n  return SentryRoutes;\n}\n\nfunction getActiveRootSpan(): Span | undefined {\n  const span = getActiveSpan();\n  const rootSpan = span ? getRootSpan(span) : undefined;\n\n  if (!rootSpan) {\n    return undefined;\n  }\n\n  const op = spanToJSON(rootSpan).op;\n\n  // Only use this root span if it is a pageload or navigation span\n  return op === 'navigation' || op === 'pageload' ? rootSpan : undefined;\n}\n\n/**\n * Returns number of URL segments of a passed string URL.\n */\nexport function getNumberOfUrlSegments(url: string): number {\n  // split at '/' or at '\\/' to split regex urls correctly\n  return url.split(/\\\\?\\//).filter(s => s.length > 0 && s !== ',').length;\n}\n", "import type { browserTracingIntegration } from '@sentry/browser';\nimport type { Integration } from '@sentry/core';\nimport type { ReactRouterOptions } from './reactrouterv6-compat-utils';\nimport {\n  createReactRouterV6CompatibleTracingIntegration,\n  createV6CompatibleWithSentryReactRouterRouting,\n  createV6CompatibleWrapCreateBrowserRouter,\n  createV6CompatibleWrapCreateMemoryRouter,\n  createV6CompatibleWrapUseRoutes,\n} from './reactrouterv6-compat-utils';\nimport type { CreateRouterFunction, Router, RouterState, UseRoutes } from './types';\n\n/**\n * A browser tracing integration that uses React Router v6 to instrument navigations.\n * Expects `useEffect`, `useLocation`, `useNavigationType`, `createRoutesFromChildren` and `matchRoutes` to be passed as options.\n */\nexport function reactRouterV6BrowserTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n): Integration {\n  return createReactRouterV6CompatibleTracingIntegration(options, '6');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v6 useRoutes hook.\n * This is used to automatically capture route changes as transactions when using the useRoutes hook.\n */\nexport function wrapUseRoutesV6(origUseRoutes: UseRoutes): UseRoutes {\n  return createV6CompatibleWrapUseRoutes(origUseRoutes, '6');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v6 createBrowserRouter function.\n * This is used to automatically capture route changes as transactions when using the createBrowserRouter API.\n */\nexport function wrapCreateBrowserRouterV6<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(createRouterFunction: CreateRouterFunction<TState, TRouter>): CreateRouterFunction<TState, TRouter> {\n  return createV6CompatibleWrapCreateBrowserRouter(createRouterFunction, '6');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v6 createMemoryRouter function.\n * This is used to automatically capture route changes as transactions when using the createMemoryRouter API.\n * The difference between createBrowserRouter and createMemoryRouter is that with createMemoryRouter,\n * optional `initialEntries` are also taken into account.\n */\nexport function wrapCreateMemoryRouterV6<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(createMemoryRouterFunction: CreateRouterFunction<TState, TRouter>): CreateRouterFunction<TState, TRouter> {\n  return createV6CompatibleWrapCreateMemoryRouter(createMemoryRouterFunction, '6');\n}\n\n/**\n * A higher-order component that adds Sentry routing instrumentation to a React Router v6 Route component.\n * This is used to automatically capture route changes as transactions.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withSentryReactRouterV6Routing<P extends Record<string, any>, R extends React.FC<P>>(routes: R): R {\n  return createV6CompatibleWithSentryReactRouterRouting<P, R>(routes, '6');\n}\n", "// React Router v7 uses the same integration as v6\nimport type { browserTracingIntegration } from '@sentry/browser';\nimport type { Integration } from '@sentry/core';\nimport type { ReactRouterOptions } from './reactrouterv6-compat-utils';\nimport {\n  createReactRouterV6CompatibleTracingIntegration,\n  createV6CompatibleWithSentryReactRouterRouting,\n  createV6CompatibleWrapCreateBrowserRouter,\n  createV6CompatibleWrapCreateMemoryRouter,\n  createV6CompatibleWrapUseRoutes,\n} from './reactrouterv6-compat-utils';\nimport type { CreateRouterFunction, Router, RouterState, UseRoutes } from './types';\n\n/**\n * A browser tracing integration that uses React Router v7 to instrument navigations.\n * Expects `useEffect`, `useLocation`, `useNavigationType`, `createRoutesFromChildren` and `matchRoutes` to be passed as options.\n */\nexport function reactRouterV7BrowserTracingIntegration(\n  options: Parameters<typeof browserTracingIntegration>[0] & ReactRouterOptions,\n): Integration {\n  return createReactRouterV6CompatibleTracingIntegration(options, '7');\n}\n\n/**\n * A higher-order component that adds Sentry routing instrumentation to a React Router v7 Route component.\n * This is used to automatically capture route changes as transactions.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withSentryReactRouterV7Routing<P extends Record<string, any>, R extends React.FC<P>>(routes: R): R {\n  return createV6CompatibleWithSentryReactRouterRouting<P, R>(routes, '7');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v7 createBrowserRouter function.\n * This is used to automatically capture route changes as transactions when using the createBrowserRouter API.\n */\nexport function wrapCreateBrowserRouterV7<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(createRouterFunction: CreateRouterFunction<TState, TRouter>): CreateRouterFunction<TState, TRouter> {\n  return createV6CompatibleWrapCreateBrowserRouter(createRouterFunction, '7');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v7 createMemoryRouter function.\n * This is used to automatically capture route changes as transactions when using the createMemoryRouter API.\n * The difference between createBrowserRouter and createMemoryRouter is that with createMemoryRouter,\n * optional `initialEntries` are also taken into account.\n */\nexport function wrapCreateMemoryRouterV7<\n  TState extends RouterState = RouterState,\n  TRouter extends Router<TState> = Router<TState>,\n>(createMemoryRouterFunction: CreateRouterFunction<TState, TRouter>): CreateRouterFunction<TState, TRouter> {\n  return createV6CompatibleWrapCreateMemoryRouter(createMemoryRouterFunction, '7');\n}\n\n/**\n * A wrapper function that adds Sentry routing instrumentation to a React Router v7 useRoutes hook.\n * This is used to automatically capture route changes as transactions when using the useRoutes hook.\n */\nexport function wrapUseRoutesV7(origUseRoutes: UseRoutes): UseRoutes {\n  return createV6CompatibleWrapUseRoutes(origUseRoutes, '7');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAIA,YAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAWA;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,UAAU;AAMd,QAAI,gBAAgB;AAAA,MAClB,mBAAmB;AAAA,MACnB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,0BAA0B;AAAA,MAC1B,0BAA0B;AAAA,MAC1B,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,gBAAgB;AAAA,MAClB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AACA,QAAI,sBAAsB;AAAA,MACxB,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AACA,QAAI,eAAe;AAAA,MACjB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,cAAc;AAAA,MACd,aAAa;AAAA,MACb,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AACA,QAAI,eAAe,CAAC;AACpB,iBAAa,QAAQ,UAAU,IAAI;AACnC,iBAAa,QAAQ,IAAI,IAAI;AAE7B,aAAS,WAAW,WAAW;AAE7B,UAAI,QAAQ,OAAO,SAAS,GAAG;AAC7B,eAAO;AAAA,MACT;AAGA,aAAO,aAAa,UAAU,UAAU,CAAC,KAAK;AAAA,IAChD;AAEA,QAAI,iBAAiB,OAAO;AAC5B,QAAI,sBAAsB,OAAO;AACjC,QAAI,wBAAwB,OAAO;AACnC,QAAI,2BAA2B,OAAO;AACtC,QAAI,iBAAiB,OAAO;AAC5B,QAAI,kBAAkB,OAAO;AAC7B,aAASC,sBAAqB,iBAAiB,iBAAiB,WAAW;AACzE,UAAI,OAAO,oBAAoB,UAAU;AAEvC,YAAI,iBAAiB;AACnB,cAAI,qBAAqB,eAAe,eAAe;AAEvD,cAAI,sBAAsB,uBAAuB,iBAAiB;AAChE,YAAAA,sBAAqB,iBAAiB,oBAAoB,SAAS;AAAA,UACrE;AAAA,QACF;AAEA,YAAI,OAAO,oBAAoB,eAAe;AAE9C,YAAI,uBAAuB;AACzB,iBAAO,KAAK,OAAO,sBAAsB,eAAe,CAAC;AAAA,QAC3D;AAEA,YAAI,gBAAgB,WAAW,eAAe;AAC9C,YAAI,gBAAgB,WAAW,eAAe;AAE9C,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,cAAc,GAAG,KAAK,EAAE,aAAa,UAAU,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,MAAM,EAAE,iBAAiB,cAAc,GAAG,IAAI;AAC7I,gBAAI,aAAa,yBAAyB,iBAAiB,GAAG;AAE9D,gBAAI;AAEF,6BAAe,iBAAiB,KAAK,UAAU;AAAA,YACjD,SAAS,GAAG;AAAA,YAAC;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAUA;AAAA;AAAA;;;;AC7FV,SAASC,MAAK,SAA6C;AAChE,QAAM,OAAO;IACX,GAAG;EACP;AAEE,mBAAiB,MAAM,OAAO;AAC9B,aAAW,SAAS,EAAE,8BAAA,CAAS;AAC/B,SAAOC,KAAY,IAAI;AACzB;;;;ACTO,SAAS,iBAAiB,cAA+B;AAC9D,QAAM,aAAa,aAAa,MAAM,UAAU;AAChD,SAAO,eAAe,QAAQ,SAAS,WAAW,CAAC,CAAC,KAAK;AAC3D;AAKO,SAAS,SAAS,OAAkC,OAAoB;AAC7E,QAAM,aAAa,oBAAI,QAAO;AAE9B,WAAS,QAAQC,QAAkCC,QAAoB;AAGrE,QAAI,WAAW,IAAID,MAAK,GAAG;AACzB;IACN;AACI,QAAIA,OAAM,OAAO;AACf,iBAAW,IAAIA,MAAK;AACpB,aAAO,QAAQA,OAAM,OAAOC,MAAK;IACvC;AACI,IAAAD,OAAM,QAAQC;EAClB;AAEE,UAAQ,OAAO,KAAK;AACtB;AAUO,SAAS,sBAEd,OACA,EAAE,eAAA,GACF,MACQ;AASR,MAAI,iBAAiB,qBAAO,KAAK,QAAQ,KAAK,KAAK,gBAAgB;AACjE,UAAM,qBAAqB,IAAI,MAAM,MAAM,OAAO;AAClD,uBAAmB,OAAO,uBAAuB,MAAM,IAAI;AACC,uBAAA,QAAA;AAGA,aAAA,OAAA,kBAAA;EACA;AAEA,SAAA,UAAA,WAAA;AACA,UAAA,WAAA,SAAA,EAAA,eAAA,CAAA;AACA,WAAA,iBAAA,OAAA,IAAA;EACA,CAAA;AACA;AAoBA,SAAA,kBAEA,UAEA;AAEA,SAAA,CAAA,OAAA,cAAA;AACA,UAAA,UAAA,sBAAA,OAAA,SAAA;AACA,QAAA,UAAA;AACA,eAAA,OAAA,WAAA,OAAA;IACA;EACA;AACA;A;;;;;ACtGzD,IAAM,kBAAkB;AAExB,IAAM,kBAAkB;AAExB,IAAM,iBAAiB;;;;ICAjB,uBAAkD,sCAAW;;;ACGnE,IAAM,oBAAoB;AAsBjC,IAAM,WAAN,cAA6B,gBAAyB;;;;;;;;EAW7C,YAAY,OAAsB;AACvC,UAAM,KAAK;AACX,UAAM,EAAE,MAAM,WAAW,MAAA,IAAU,KAAK;AAExC,QAAI,UAAU;AACZ;IACN;AAEI,SAAK,aAAa,kBAAkB;MAClC,MAAM,IAAI,IAAI;MACd,cAAc;MACd,IAAI;MACJ,YAAY;QACV,CAAC,gCAAgC,GAAG;QACpC,qBAAqB;MAC7B;IACA,CAAK;EACL;;EAGS,oBAA0B;AAC/B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,IAAG;IACzB;EACA;EAES,sBAAsB,EAAE,aAAa,iBAAiB,KAAA,GAAgC;AAI3F,QAAI,kBAAkB,KAAK,cAAc,gBAAgB,KAAK,MAAM,aAAa;AAG/E,YAAM,eAAe,OAAO,KAAK,WAAW,EAAE,OAAO,OAAK,YAAY,CAAC,MAAM,KAAK,MAAM,YAAY,CAAC,CAAC;AACtG,UAAI,aAAa,SAAS,GAAG;AAC3B,cAAM,MAAM,mBAAkB;AAC9B,aAAK,cAAc,eAAe,KAAK,YAAY,MAAM;AACvD,iBAAO,kBAAkB;YACvB,MAAM,IAAI,KAAK,MAAM,IAAI;YACzB,cAAc;YACd,IAAI;YACJ,WAAW;YACX,YAAY;cACV,CAAC,gCAAgC,GAAG;cACpC,qBAAqB,KAAK,MAAM;cAChC,0BAA0B;YACxC;UACA,CAAW;QACX,CAAS;MACT;IACA;AAEI,WAAO;EACX;EAES,qBAA2B;AAChC,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,IAAG;AACpB,WAAK,cAAc;IACzB;EACA;;;EAIS,uBAA6B;AAClC,UAAM,eAAe,mBAAkB;AACvC,UAAM,EAAE,MAAM,gBAAgB,KAAA,IAAS,KAAK;AAE5C,QAAI,KAAK,cAAc,eAAe;AACpC,YAAM,YAAY,WAAW,KAAK,UAAU,EAAE;AAC9C,qBAAe,KAAK,YAAY,MAAM;AACpC,cAAM,aAAa,kBAAkB;UACnC,cAAc;UACd,MAAM,IAAI,IAAI;UACd,IAAI;UACJ;UACA,YAAY;YACV,CAAC,gCAAgC,GAAG;YACpC,qBAAqB;UACjC;QACA,CAAS;AACD,YAAI,YAAY;AAGd,qBAAW,IAAI,YAAY;QACrC;MACA,CAAO;IACP;EACA;EAES,SAA0B;AAC/B,WAAO,KAAK,MAAM;EACtB;AACA;AAGA,OAAO,OAAO,UAAU;EACtB,cAAc;IACZ,UAAU;IACV,eAAe;IACf,gBAAgB;EACpB;AACA,CAAC;AAWD,SAAS,aACP,kBAEA,SACa;AACb,QAAM,uBACJ,SAAS,QAAQ,iBAAiB,eAAe,iBAAiB,QAAQ;AAE5E,QAAM,UAAuB,CAAC,UAC5B;IAAC;IAAA,EAAS,GAAI,SAAS,MAAK,sBAAuB,aAAY,MAAM;IACnE,oBAAC,kBAAA,EAAiB,GAAI,MAAK,CAAA;EACjC;AAGE,UAAQ,cAAc,YAAY,oBAAoB;AAItD,uBAAqB,SAAS,gBAAgB;AAC9C,SAAO;AACT;AASA,SAAS,YACP,MACA,UAA2D;EACzD,UAAU;EACV,eAAe;AACnB,GACQ;AACN,QAAM,CAAC,SAAS,IAAU,eAAS,MAAM;AACvC,QAAI,SAAS,UAAU;AACrB,aAAO;IACb;AAEI,WAAO,kBAAkB;MACvB,MAAM,IAAI,IAAI;MACd,cAAc;MACd,IAAI;MACJ,YAAY;QACV,CAAC,gCAAgC,GAAG;QACpC,qBAAqB;MAC7B;IACA,CAAK;EACL,CAAG;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,WAAW;AACb,gBAAU,IAAG;IACnB;AAEI,WAAO,MAAY;AACjB,UAAI,aAAa,QAAQ,eAAe;AACtC,cAAM,YAAY,WAAW,SAAS,EAAE;AACxC,cAAM,eAAe,mBAAkB;AAEvC,cAAM,aAAa,kBAAkB;UACnC,MAAM,IAAI,IAAI;UACd,cAAc;UACd,IAAI;UACJ;UACA,YAAY;YACV,CAAC,gCAAgC,GAAG;YACpC,qBAAqB;UACjC;QACA,CAAS;AACD,YAAI,YAAY;AAGd,qBAAW,IAAI,YAAY;QACrC;MACA;IACA;EAGA,GAAK,CAAA,CAAE;AACP;A;;;;;ACpOO,IAAM,cAAc,OAAA,qBAAA,eAAA;;;ACEpB,IAAMC,qBAAoB;AAsEjC,IAAM,gBAAoC;EACxC,gBAAgB;EAChB,OAAO;EACP,SAAS;AACX;AAQA,IAAM,gBAAN,cAAkC,iBAAkD;EAQ3E,YAAY,OAA2B;AAC5C,UAAM,KAAK;AAEX,SAAK,QAAQ;AACb,SAAK,4BAA4B;AAEjC,UAAM,SAAS,UAAS;AACxB,QAAI,UAAU,MAAM,YAAY;AAC9B,WAAK,4BAA4B;AACjC,WAAK,eAAe,OAAO,GAAG,kBAAkB,WAAS;AACvD,YAAI,CAAC,MAAM,QAAQ,KAAK,gBAAgB,MAAM,aAAa,KAAK,cAAc;AAC5E,2BAAiB,EAAE,GAAG,MAAM,eAAe,SAAS,KAAK,aAAA,CAAc;QACjF;MACA,CAAO;IACP;EACA;EAES,kBAAkB,OAAgB,WAAkC;AACzE,UAAM,EAAE,eAAA,IAAmB;AAC3B,UAAM,EAAE,eAAe,SAAS,YAAY,cAAA,IAAkB,KAAK;AACnE,cAAU,WAAS;AACjB,UAAI,eAAe;AACjB,sBAAc,OAAO,OAAO,cAAc;MAClD;AAEM,YAAM,UAAU,KAAK,MAAM,WAAW,OAAO,KAAK,MAAM,UAAU,CAAC,CAAC,KAAK,MAAM;AAC/E,YAAM,UAAU,sBAAsB,OAAO,WAAW,EAAE,WAAW,EAAE,QAAA,EAAQ,CAAG;AAElF,UAAI,SAAS;AACX,gBAAQ,OAAO,gBAAgB,OAAO;MAC9C;AACM,UAAI,YAAY;AACd,aAAK,eAAe;AACpB,YAAI,KAAK,2BAA2B;AAClC,2BAAiB,EAAE,GAAG,eAAe,QAAA,CAAS;QACxD;MACA;AAIM,WAAK,SAAS,EAAE,OAAO,gBAAgB,QAAA,CAAS;IACtD,CAAK;EACL;EAES,oBAA0B;AAC/B,UAAM,EAAE,QAAA,IAAY,KAAK;AACzB,QAAI,SAAS;AACX,cAAO;IACb;EACA;EAES,uBAA6B;AAClC,UAAM,EAAE,OAAO,gBAAgB,QAAA,IAAY,KAAK;AAChD,UAAM,EAAE,UAAA,IAAc,KAAK;AAC3B,QAAI,WAAW;AACb,UAAI,KAAK,UAAU,eAAe;AAEhC,kBAAU,MAAM,MAAM,IAAI;MAClC,OAAa;AAIL,kBAAU,OAAO,gBAAiB,OAAO;MACjD;IACA;AAEI,QAAI,KAAK,cAAc;AACrB,WAAK,aAAY;AACjB,WAAK,eAAe;IAC1B;EACA;EAES,qBAA2B;AAChC,UAAM,EAAE,QAAA,IAAY,KAAK;AACzB,UAAM,EAAE,OAAO,gBAAgB,QAAA,IAAY,KAAK;AAChD,QAAI,SAAS;AAIX,cAAQ,OAAO,gBAAiB,OAAO;IAC7C;AACI,SAAK,SAAS,aAAa;EAC/B;EAES,SAA0B;AAC/B,UAAM,EAAE,UAAU,SAAA,IAAa,KAAK;AACpC,UAAM,QAAQ,KAAK;AAKnB,QAAI,MAAM,mBAAmB,MAAM;AACjC,aAAO,OAAO,aAAa,aAAa,SAAQ,IAAK;IAC3D;AAEI,UAAM,UACJ,OAAO,aAAa,aACV,qBAAc,UAAU;MAC5B,OAAO,MAAM;MACb,gBAAgB,MAAM;MACtB,YAAY,MAAM,KAAK,mBAAkB;MACzC,SAAS,MAAM;IAC3B,CAAW,IACD;AAEN,QAAU,sBAAe,OAAO,GAAG;AACjC,aAAO;IACb;AAEI,QAAI,UAAU;AACZ,qBAAe,OAAO,KAAK,+CAA+C;IAChF;AAGI,WAAO;EACX;AACA;AAGA,SAAS,kBACP,kBACA,sBACa;AACb,QAAM,uBAAuB,iBAAiB,eAAe,iBAAiB,QAAQA;AAEtF,QAAM,UAAuB,CAAC,UAC5B;IAAC;IAAA,EAAc,GAAI,qBAAoB;IACrC,qBAAC,kBAAA,EAAiB,GAAI,MAAK,CAAA;EACjC;AAGE,UAAQ,cAAc,iBAAiB,oBAAoB;AAI3D,uBAAqB,SAAS,gBAAgB;AAC9C,SAAO;AACT;;;AClKA,IAAM,6BAA6B;AACnC,IAAM,yBAAyB;AAE/B,IAAM,iBAAwC;EAC5C,kBAAkB;EAClB,mBAAmB,YAAU;EAC7B,kBAAkB,WAAS,SAAS;AACtC;AAOA,SAAS,oBAAoB,iBAAuD;AAElF,QAAM,UAAU;IACd,GAAG;IACH,GAAG;EACP;AAEE,SAAO,CAAC,SACN,CAAwC,SAAwB,iBAAqC;AACnG,YAAQ,oBACN,eAAc,EAAG,kBAAkB,CAAC,OAAO,SAAS;AAClD,UAAI;AAEF,YAAI,MAAM,SAAS,UAAa,MAAM,SAAS,MAAM,MAAM,SAAS,SAAS;AAC3E,eAAK,cAAc;YACjB,GAAI,KAAK,eAAe,CAAA;;YAExB,EAAE,UAAU,oBAAoB,MAAM,KAAK,UAAU,MAAM,SAAS,MAAM,MAAM,KAAK,EAAA;UACrG;QACA;MACA,SAAmB,GAAG;MAEtB;AACU,aAAO;IACjB,CAAS;AAEH,aAAS,kBAAkBC,UAAuC;AAChE,aAAO,CAAC,OAAO,WAAc;AAC3B,cAAM,WAAWA,SAAQ,OAAO,MAAM;AAEtC,cAAM,QAAQ,gBAAe;AAG7B,cAAM,oBAAoB,QAAQ,kBAAkB,MAAM;AAC1D,YAAI,OAAO,sBAAsB,eAAe,sBAAsB,MAAM;AAC1E,wBAAc;YACZ,UAAU;YACV,MAAM;YACN,MAAM;UACpB,CAAa;QACb;AAGU,cAAM,mBAAmB,QAAQ,iBAAiB,QAAQ;AAC1D,YAAI,OAAO,qBAAqB,eAAe,qBAAqB,MAAM;AACxE,gBAAM,SAAS,UAAS;AACxB,gBAAMC,WAAU,QAAQ,WAAU;AAClC,gBAAM,qBAAqBA,UAAS,kBAAkB;AAGtD,gBAAM,kBAAkB,EAAE,OAAO,EAAE,MAAM,SAAS,OAAO,iBAAA,EAAA;AACzD;YACE;YACA;YACA;YACE;;UAChB;AAEY,gBAAM,WAAW,SAAS,eAAe;QACrD,OAAiB;AACL,gBAAM,WAAW,SAAS,IAAI;QAC1C;AAGU,cAAM,EAAE,wBAAA,IAA4B;AACpC,YAAI,OAAO,4BAA4B,YAAY;AACjD,kCAAwB,OAAO,QAAQ;QACnD;AAEU,eAAO;MACjB;IACA;AAEM,UAAM,QAAQ,KAAK,kBAAkB,OAAO,GAAG,YAAY;AAG3D,UAAM,iBAAiB,IAAI,MAAM,MAAM,gBAAgB;MACrD,OAAO,SAAU,QAAQ,SAAS,MAAM;AACtC,eAAO,MAAM,SAAS,CAAC,kBAAkB,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5D;IACA,CAAO;AAED,WAAO;EACb;AACA;;;AClIO,SAAS,uCACd,SACa;AACb,QAAM,cAAc,0BAA0B;IAC5C,GAAG;IACH,oBAAoB;IACpB,sBAAsB;EAC1B,CAAG;AAED,QAAM,EAAE,SAAS,QAAQ,OAAO,qBAAqB,MAAM,uBAAuB,KAAA,IAAS;AAE3F,SAAO;IACL,GAAG;IACH,cAAc,QAAQ;AACpB,kBAAY,cAAc,MAAM;AAEhC,UAAI,sBAAsB,OAAO,UAAU;AACzC;UACE;UACA,OAAO;UACP;UACA,CAAC,WAAmB,SAAyC,UAAU;AACrE,4CAAgC,QAAQ;cACtC,MAAM;cACN,YAAY;gBACV,CAAC,4BAA4B,GAAG;gBAChC,CAAC,gCAAgC,GAAG;gBACpC,CAAC,gCAAgC,GAAG;cACpD;YACA,CAAa;UACb;QACA;MACA;AAEM,UAAI,wBAAwB,QAAQ,QAAQ;AAC1C,gBAAQ,OAAO,cAAY;AACzB,cAAI,SAAS,WAAW,UAAU,SAAS,WAAW,OAAO;AAC3D;cACE;cACA;cACA;cACA,CAAC,WAAmB,SAA4B,UAAU;AACxD,kDAAkC,QAAQ;kBACxC,MAAM;kBACN,YAAY;oBACV,CAAC,4BAA4B,GAAG;oBAChC,CAAC,gCAAgC,GAAG;oBACpC,CAAC,gCAAgC,GAAG;kBACxD;gBACA,CAAiB;cACjB;YACA;UACA;QACA,CAAS;MACT;IACA;EACA;AACA;AAKA,SAAS,yBACP,WACA,UACA,OACA,UACM;AACN,MAAI,OAAO,SAAS;AACpB;IACE;MACE;MACA,QAAQ;IACd;IACI,CAAC,OAAO,mBAAmB,gBAAgB;AACzC,UAAI,SAAS,CAAC,aAAa;AACzB,eAAO,SAAS,IAAI;MAC5B;AAEM,YAAM,YAAY,yBAAyB,YAAY,UAAU,CAAA,CAAE;AACnE,UAAI,UAAU,WAAW,KAAK,cAAc,MAAM;AAChD,eAAO,SAAS,IAAI;MAC5B;AAEM,aAAO;AACP,aAAO,SAAS,MAAM,OAAO;IACnC;EACA;AACA;AAKA,SAAS,yBAAyB,QAAyB;AACzD,MAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW,GAAG;AACjD,WAAO;EACX;AAEE,QAAM,kBAA2B,OAAO,OAAO,CAAC,UAAiB,CAAC,CAAC,MAAM,IAAI;AAE7E,MAAI,QAAQ;AACZ,WAAS,IAAI,gBAAgB,SAAS,GAAG,KAAK,GAAG,KAAK;AAEpD,UAAM,QAAQ,gBAAgB,CAAC;AAC/B,QAAI,MAAM,MAAM,WAAW,GAAG,GAAG;AAC/B,cAAQ;AACR;IACN;EACA;AAEE,SAAO,gBAAgB,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,KAAA,MAAW;AAC5D,UAAM,cAAc,QAAQ,OAAO,QAAQ,KAAK,OAAO,IAAI,IAAI;AACC,WAAA,GAAA,GAAA,GAAA,WAAA;EACA,GAAA,EAAA;AACA;;;ACtI7D,SAAS,wCAEd,QACA,UAAmE,CAAA,GACtD;AACb,QAAM,qBAA6C;AAEnD,QAAM,oCAAoCC,0BAAkC;IAC1E,GAAG;IACH,sBAAsB;IACtB,oBAAoB;EACxB,CAAG;AAED,QAAM,EAAE,qBAAqB,MAAM,uBAAuB,KAAA,IAAS;AAEnE,SAAO;IACL,GAAG;IACH,cAAc,QAAQ;AACpB,wCAAkC,cAAc,MAAM;AAEtD,YAAM,wBAAwB,OAAO;AACrC,UAAI,sBAAsB,uBAAuB;AAC/C,cAAM,gBAAgB,mBAAmB;UACvC,sBAAsB;UACtB,mBAAmB,QAAQ,YAAY,sBAAsB,MAAM;UACnE,EAAE,SAAS,OAAO,cAAc,MAAA;QAC1C;AAEQ,cAAM,YAAY,cAAc,cAAc,SAAS,CAAC;AAExD,wCAAgC,QAAQ;UACtC,MAAM,YAAY,UAAU,UAAU,sBAAsB;UAC5D,YAAY;YACV,CAAC,4BAA4B,GAAG;YAChC,CAAC,gCAAgC,GAAG;YACpC,CAAC,gCAAgC,GAAG,YAAY,UAAU;YAC1D,GAAG,gCAAgC,SAAS;UACxD;QACA,CAAS;MACT;AAEM,UAAI,sBAAsB;AAExB,2BAAmB,UAAU,oBAAoB,0BAAwB;AAEvE,cAAI,qBAAqB,WAAW,UAAU,qBAAqB,cAAc,OAAO;AACtF;UACZ;AAEU,gBAAM,0BAA0B,mBAAmB;YACjD,qBAAqB,WAAW;YAChC,qBAAqB,WAAW;YAChC,EAAE,SAAS,OAAO,cAAc,MAAA;UAC5C;AAEU,gBAAM,4BAA4B,wBAAwB,wBAAwB,SAAS,CAAC;AAE5F,gBAAM,qBAAqB,OAAO;AAClC,gBAAM,iBAAiB,kCAAkC,QAAQ;YAC/D,MAAM,4BAA4B,0BAA0B,UAAU,mBAAmB;YACzF,YAAY;cACV,CAAC,4BAA4B,GAAG;cAChC,CAAC,gCAAgC,GAAG;cACpC,CAAC,gCAAgC,GAAG,4BAA4B,UAAU;YACxF;UACA,CAAW;AAGD,gBAAM,wBAAwB,mBAAmB,UAAU,cAAc,oBAAkB;AACzF,kCAAqB;AACrB,gBAAI,gBAAgB;AAClB,oBAAMC,2BAA0B,mBAAmB;gBACjD,eAAe,WAAW;gBAC1B,eAAe,WAAW;gBAC1B,EAAE,SAAS,OAAO,cAAc,MAAA;cAChD;AAEc,oBAAM,sBAAsBA,yBAAwBA,yBAAwB,SAAS,CAAC;AAEtF,kBAAI,qBAAqB;AACvB,+BAAe,WAAW,oBAAoB,OAAO;AACrD,+BAAe,aAAa,kCAAkC,OAAO;AACrE,+BAAe,cAAc,gCAAgC,mBAAmB,CAAC;cACjG;YACA;UACA,CAAW;QACX,CAAS;MACT;IACA;EACA;AACA;AAEA,SAAS,gCAAgC,OAA6E;AACpH,MAAI,CAAC,OAAO;AACV,WAAO,CAAA;EACX;AAEE,QAAM,kBAA0C,CAAA;AAChD,SAAO,QAAQ,MAAM,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACrD,oBAAgB,mBAAmB,GAAG,EAAC,IAAA;AACA,oBAAA,sBAAA,GAAA,EAAA,IAAA;AACA,oBAAA,UAAA,GAAA,EAAA,IAAA;EACA,CAAA;AAEA,SAAA;AACA;A;;;AC7EpC,SAAS,uCACd,SACa;AACb,QAAM,cAAc,0BAA0B;IAC5C,GAAG;IACH,oBAAoB;IACpB,sBAAsB;EAC1B,CAAG;AAED,QAAM,EAAE,SAAS,QAAQ,WAAW,qBAAqB,MAAM,uBAAuB,KAAA,IAAS;AAE/F,SAAO;IACL,GAAG;IACH,cAAc,QAAQ;AACpB,kBAAY,cAAc,MAAM;AAEhC;QACE;QACA;QACA;QACA;QACA;QACA;QACA;MACR;IACA;EACA;AACA;AAMO,SAAS,uCACd,SACa;AACb,QAAM,cAAc,0BAA0B;IAC5C,GAAG;IACH,oBAAoB;IACpB,sBAAsB;EAC1B,CAAG;AAED,QAAM,EAAE,SAAS,QAAQ,WAAW,qBAAqB,MAAM,uBAAuB,KAAA,IAAS;AAE/F,SAAO;IACL,GAAG;IACH,cAAc,QAAQ;AACpB,kBAAY,cAAc,MAAM;AAEhC;QACE;QACA;QACA;QACA;QACA;QACA;QACA;MACR;IACA;EACA;AACA;AAEA,SAAS,sBACP,QACA,oBACA,sBACA,SACA,qBACAC,aAA2B,CAAA,GAC3B,WACM;AACN,WAAS,kBAAsC;AAC7C,QAAI,QAAQ,UAAU;AACpB,aAAO,QAAQ,SAAS;IAC9B;AAEI,QAAI,OAAO,UAAU;AACnB,aAAO,OAAO,SAAS;IAC7B;AAEI,WAAO;EACX;AAQE,WAASC,0BAAyB,UAA+C;AAC/E,QAAID,WAAU,WAAW,KAAK,CAAC,WAAW;AACxC,aAAO,CAAC,UAAU,KAAK;IAC7B;AAEI,UAAM,WAAW,YAAYA,YAAW,UAAU,SAAS;AAC3D,eAAW,UAAU,UAAU;AAC7B,UAAI,OAAO,MAAM,SAAS;AACxB,eAAO,CAAC,OAAO,MAAM,MAAM,OAAO;MAC1C;IACA;AAEI,WAAO,CAAC,UAAU,KAAK;EAC3B;AAEE,MAAI,oBAAoB;AACtB,UAAM,eAAe,gBAAe;AACpC,QAAI,cAAc;AAChB,YAAM,CAAC,MAAM,MAAM,IAAIC,0BAAyB,YAAY;AAC5D,sCAAgC,QAAQ;QACtC;QACA,YAAY;UACV,CAAC,4BAA4B,GAAG;UAChC,CAAC,gCAAgC,GAAG,uBAAuB,mBAAmB;UACC,CAAA,gCAAA,GAAA;QACA;MACA,CAAA;IACA;EACA;AAEA,MAAA,wBAAA,QAAA,QAAA;AACA,YAAA,OAAA,CAAA,UAAA,WAAA;AACA,UAAA,WAAA,WAAA,UAAA,WAAA,QAAA;AACA,cAAA,CAAA,MAAA,MAAA,IAAAA,0BAAA,SAAA,QAAA;AACA,0CAAA,QAAA;UACA;UACA,YAAA;YACA,CAAA,4BAAA,GAAA;YACA,CAAA,gCAAA,GAAA,yBAAA,mBAAA;YACA,CAAA,gCAAA,GAAA;UACA;QACA,CAAA;MACA;IACA,CAAA;EACA;AACA;AAMA,SAAA,YACA,QACA,UACA,WACA,SAAA,CAAA,GACA;AACA,SAAA,KAAA,WAAA;AACA,UAAA,QAAA,MAAA,OACA,UAAA,UAAA,KAAA,IACA,OAAA;;MAEA,OAAA,OAAA,SAAA,CAAA,EAAA;QACA,iBAAA,QAAA;AAEA,QAAA,OAAA;AACA,aAAA,KAAA,EAAA,OAAA,MAAA,CAAA;AAEA,UAAA,MAAA,QAAA;AACA,oBAAA,MAAA,QAAA,UAAA,WAAA,MAAA;MACA;IACA;AAEA,WAAA,CAAA,CAAA;EACA,CAAA;AAEA,SAAA;AACA;AAEA,SAAA,iBAAA,UAAA;AACA,SAAA,EAAA,MAAA,KAAA,KAAA,KAAA,QAAA,CAAA,GAAA,SAAA,aAAA,IAAA;AACA;AAGA,SAAA,kBAAA,OAAA;AACA,QAAA,uBAAA,MAAA,eAAA,MAAA;AAEA,QAAA,eAAA,CAAA,UAAA;AACA,QAAA,OAAA,eAAA,SAAA;AACA,YAAA,QAAA,MAAA,cAAA;AACA,YAAA,iBAAA,kBAAA;AAEA,sBAAA,EAAA,mBAAA,KAAA;AAEA,UAAA,gBAAA;AACA,uBAAA,WAAA,KAAA;AACA,uBAAA,aAAA,kCAAA,OAAA;MACA;IACA;AAKA,WAAA,qBAAA,OAAA,EAAA,GAAA,MAAA,CAAA;EACA;AAEA,eAAA,cAAA,eAAA,oBAAA;AACA,uBAAA,cAAA,KAAA;AAIA,SAAA;AACA;AAGA,SAAA,oBAAA;AACA,QAAA,OAAA,cAAA;AACA,QAAA,WAAA,QAAA,YAAA,IAAA;AAEA,MAAA,CAAA,UAAA;AACA,WAAA;EACA;AAEA,QAAA,KAAA,WAAA,QAAA,EAAA;AAGA,SAAA,OAAA,gBAAA,OAAA,aAAA,WAAA;AACA;A;;;AChOzF,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,iBAA0B;AAE9B,IAAM,qCAAqC,oBAAI,QAAO;AActD,IAAM,YAAY,oBAAI,IAAG;AAKlB,SAAS,0CAId,sBACAC,UACuC;AACvC,MAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,cAAc;AACxE,mBACE,OAAO;MACL,eAAeA,QAAO;IAC9B;AAEI,WAAO;EACX;AAEE,SAAO,SAAU,QAAuB,MAAiE;AACvG,yBAAqB,MAAM;AAE3B,UAAM,SAAS,qBAAqB,QAAQ,IAAI;AAChD,UAAM,WAAW,MAAM;AAEvB,UAAM,iBAAiBC,mBAAiB;AAKxC,QAAI,OAAO,MAAM,kBAAkB,SAAS,gBAAgB;AAC1D;QACE;QACA,OAAO,MAAM;QACb;QACA;QACA;QACA,MAAM,KAAK,SAAS;MAC5B;IACA;AAEI,WAAO,UAAU,CAAC,UAAuB;AACvC,UAAI,MAAM,kBAAkB,UAAU,MAAM,kBAAkB,OAAO;AAEnE,YAAI,MAAM,WAAW,UAAU,QAAQ;AACrC,gCAAsB,MAAM;AAC1B,6BAAiB;cACf,UAAU,MAAM;cAChB;cACA,gBAAgB,MAAM;cACtB,SAAAD;cACA;cACA,WAAW,MAAM,KAAK,SAAS;YAC7C,CAAa;UACb,CAAW;QACX,OAAe;AACL,2BAAiB;YACf,UAAU,MAAM;YAChB;YACA,gBAAgB,MAAM;YACtB,SAAAA;YACA;YACA,WAAW,MAAM,KAAK,SAAS;UAC3C,CAAW;QACX;MACA;IACA,CAAK;AAED,WAAO;EACX;AACA;AAKO,SAAS,yCAId,sBACAA,UACuC;AACvC,MAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,cAAc;AACxE,mBACE,OAAO;MACL,eAAeA,QAAO;IAC9B;AAEI,WAAO;EACX;AAEE,SAAO,SACL,QACA,MAKS;AACT,yBAAqB,MAAM;AAE3B,UAAM,SAAS,qBAAqB,QAAQ,IAAI;AAChD,UAAM,WAAW,MAAM;AAEvB,UAAM,iBAAiBC,mBAAiB;AACxC,QAAI,eAAe;AAEnB,UAAM,iBAAiB,MAAM;AAC7B,UAAM,eAAe,MAAM;AAE3B,UAAM,yBAAyB,kBAAkB,eAAe,WAAW;AAC3E,UAAM,kBAAkB,iBAAiB,UAAa,kBAAkB,eAAe,YAAY;AAEnG,mBAAe,yBACX,eAAe,CAAC,IAChB,kBACE,eAAe,YAAY,IAC3B;AAEN,UAAM,WAAW,eACb,OAAO,iBAAiB,WACtB,EAAE,UAAU,aAAA,IACZ,eACF,OAAO,MAAM;AAEjB,QAAI,OAAO,MAAM,kBAAkB,SAAS,gBAAgB;AAC1D,gCAA0B,gBAAgB,UAAU,QAAQ,QAAW,UAAU,MAAM,KAAK,SAAS,CAAC;IAC5G;AAEI,WAAO,UAAU,CAAC,UAAuB;AACvC,YAAMC,YAAW,MAAM;AACvB,UAAI,MAAM,kBAAkB,UAAU,MAAM,kBAAkB,OAAO;AACnE,yBAAiB;UACf,UAAAA;UACA;UACA,gBAAgB,MAAM;UACtB,SAAAF;UACA;UACA,WAAW,MAAM,KAAK,SAAS;QACzC,CAAS;MACT;IACA,CAAK;AAED,WAAO;EACX;AACA;AAKO,SAAS,gDACd,SACAA,UACa;AACb,QAAM,cAAc,0BAA0B;IAC5C,GAAG;IACH,oBAAoB;IACpB,sBAAsB;EAC1B,CAAG;AAED,QAAM;IACJ,WAAAG;IACA;IACA;IACA;IACA,aAAAC;IACA;IACA,qBAAqB;IACrB,uBAAuB;EAC3B,IAAM;AAEJ,SAAO;IACL,GAAG;IACH,MAAM,QAAQ;AACZ,kBAAY,MAAM,MAAM;AAExB,mBAAaD;AACb,qBAAe;AACf,2BAAqB;AACrB,qBAAeC;AACf,kCAA4B;AAC5B,uBAAiB,iBAAiB;IACxC;IACI,cAAc,QAAQ;AACpB,kBAAY,cAAc,MAAM;AAEhC,YAAM,eAAe,OAAO,UAAU;AACtC,UAAI,sBAAsB,cAAc;AACtC,wCAAgC,QAAQ;UACtC,MAAM;UACN,YAAY;YACV,CAAC,gCAAgC,GAAG;YACpC,CAAC,4BAA4B,GAAG;YAChC,CAAC,gCAAgC,GAAG,oCAAoCJ,QAAO;UACC;QACA,CAAA;MACA;AAEA,UAAA,sBAAA;AACA,2CAAA,IAAA,MAAA;MACA;IACA;EACA;AACA;AAEA,SAAA,gCAAA,eAAAA,UAAA;AACA,MAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,sBAAA,CAAA,cAAA;AACA,mBACA,OAAA;MACA;IACA;AAEA,WAAA;EACA;AAEA,QAAA,eAIA,CAAA,UAAA;AACA,UAAA,oBAAA,cAAA,IAAA;AACA,UAAA,EAAA,QAAA,YAAA,IAAA;AAEA,UAAA,SAAA,cAAA,QAAA,WAAA;AAEA,UAAA,WAAA,aAAA;AACA,UAAA,iBAAA,mBAAA;AAGA,UAAA,sBACA,OAAA,gBAAA,YAAA,aAAA,WAAA,cAAA;AAEA,eAAA,MAAA;AACA,YAAA,qBACA,OAAA,wBAAA,WAAA,EAAA,UAAA,oBAAA,IAAA;AAEA,UAAA,kBAAA,SAAA;AACA,6BAAA,MAAA;AAEA;UACAC,mBAAA;UACA;UACA;UACA;UACA;UACA,MAAA,KAAA,SAAA;QACA;AACA,0BAAA,UAAA;MACA,OAAA;AACA,yBAAA;UACA,UAAA;UACA;UACA;UACA,SAAAD;UACA,WAAA,MAAA,KAAA,SAAA;QACA,CAAA;MACA;IACA,GAAA,CAAA,gBAAA,mBAAA,CAAA;AAEA,WAAA;EACA;AAGA,SAAA,CAAA,QAAA,gBAAA;AACA,WAAA,qBAAA,cAAA,EAAA,QAAA,YAAA,CAAA;EACA;AACA;AAEA,SAAA,iBAAA,MAQA;AACA,QAAA,EAAA,UAAA,QAAA,gBAAA,SAAAA,UAAA,SAAA,UAAA,WAAAK,WAAA,IAAA;AACA,QAAA,WAAA,MAAA,QAAA,OAAA,IAAA,UAAA,aAAA,QAAA,UAAA,QAAA;AAEA,QAAA,SAAA,UAAA;AACA,MAAA,CAAA,UAAA,CAAA,mCAAA,IAAA,MAAA,GAAA;AACA;EACA;AAEA,OAAA,mBAAA,UAAA,mBAAA,UAAA,UAAA;AACA,QAAA,MACA,SAAA;AACA,UAAA,sBAAA,gCAAA,UAAAA,cAAA,MAAA;AAEA,QAAA,qBAAA;AACA,aAAA,gBAAA,8BAAAA,cAAA,QAAA,QAAA,CAAA;AACA,eAAA;IACA;AAEA,QAAA,CAAA,uBAAA,CAAA,MAAA;AACA,OAAA,MAAA,MAAA,IAAA,kBAAA,QAAA,UAAA,UAAA,QAAA;IACA;AAEA,UAAA,aAAA,cAAA;AACA,UAAA,4BAAA,cAAA,WAAA,UAAA,EAAA,OAAA;AAGA,QAAA,2BAAA;AACA,kBAAA,WAAA,IAAA;AACA,kBAAA,aAAA,kCAAA,MAAA;IACA,OAAA;AACA,wCAAA,QAAA;QACA;QACA,YAAA;UACA,CAAA,gCAAA,GAAA;UACA,CAAA,4BAAA,GAAA;UACA,CAAA,gCAAA,GAAA,sCAAAL,QAAA;QACA;MACA,CAAA;IACA;EACA;AACA;AAQA,SAAA,0BAAA,UAAA,UAAA;AACA,MAAA,CAAA,YAAA,aAAA,KAAA;AACA,WAAA;EACA;AAEA,MAAA,CAAA,SAAA,YAAA,EAAA,WAAA,SAAA,YAAA,CAAA,GAAA;AACA,WAAA;EACA;AAIA,QAAA,aAAA,SAAA,SAAA,GAAA,IAAA,SAAA,SAAA,IAAA,SAAA;AACA,QAAA,WAAA,SAAA,OAAA,UAAA;AACA,MAAA,YAAA,aAAA,KAAA;AAEA,WAAA;EACA;AAEA,SAAA,SAAA,MAAA,UAAA,KAAA;AACA;AAEA,SAAA,cAAA,aAAA,UAAA,UAAA;AACA,QAAA,oBAAA,eAAA,iBAAA,0BAAA,UAAA,QAAA,IAAA;AAEA,QAAA;;IAEA,kBAAA,kBAAA,SAAA,CAAA,MAAA,MACA,kBAAA,MAAA,GAAA,EAAA;;MAEA,kBAAA,MAAA,EAAA,MAAA,OACA,kBAAA,MAAA,GAAA,EAAA,IACA;;;AAEA,SAAA,CAAA,eAAA,OAAA;AACA;AAEA,SAAA,qBAAA,MAAA;AACA,SAAA,KAAA,SAAA,GAAA;AACA;AAEA,SAAA,6BAAA,MAAA,QAAA;AACA,SAAA,qBAAA,IAAA,KAAA,CAAA,CAAA,OAAA,MAAA,UAAA,UAAA;AACA;AAEA,SAAA,kBAAA,OAAA;AACA,SAAA,CAAA,EAAA,CAAA,MAAA,YAAA,MAAA,WAAA,MAAA,MAAA,SAAA,IAAA;AACA;AAEA,SAAA,gCAAA,UAAA,QAAA;AACA,QAAA,gBAAA,aAAA,QAAA,QAAA;AAEA,MAAA,eAAA;AACA,eAAA,SAAA,eAAA;AACA,UAAA,kBAAA,MAAA,KAAA,KAAA,UAAA,KAAA,GAAA;AACA,eAAA;MACA;IACA;EACA;AAEA,SAAA;AACA;AAEA,SAAA,qBAAA,QAAA;AACA,SAAA,QAAA,WAAA;AACA,UAAA,uBAAA,0BAAA,KAAA;AAEA,yBAAA,QAAA,OAAA;AACA,gBAAA,IAAA,CAAA;IACA,CAAA;EACA,CAAA;AACA;AAEA,SAAA,0BAAA,OAAAK,aAAA,oBAAA,IAAA,GAAA;AACA,MAAA,CAAAA,WAAA,IAAA,KAAA,GAAA;AACA,IAAAA,WAAA,IAAA,KAAA;AAEA,QAAA,MAAA,YAAA,CAAA,MAAA,OAAA;AACA,YAAA,SAAA,QAAA,WAAA;AACA,cAAA,cAAA,0BAAA,OAAAA,UAAA;AAEA,oBAAA,QAAA,OAAA;AACA,UAAAA,WAAA,IAAA,CAAA;QACA,CAAA;MACA,CAAA;IACA;EACA;AAEA,SAAAA;AACA;AAEA,SAAA,SAAA,OAAA;AACA,SAAA,aAAA,MAAA,MAAA,QAAA,EAAA;AACA;AAEA,SAAA,UAAA,OAAA;AACA,SAAA,MAAA,OAAA,GAAA,KAAA;AACA;AAEA,SAAA,aAAA,MAAA;AACA,SAAA,KAAA,KAAA,SAAA,CAAA,MAAA,MAAA,KAAA,MAAA,GAAA,EAAA,IAAA;AACA;AAEA,SAAA,UAAA,MAAA;AACA,SAAA,KAAA,KAAA,SAAA,CAAA,MAAA,MAAA,KAAA,MAAA,GAAA,EAAA,IAAA;AACA;AAEA,SAAA,gBAAA,MAAA;AACA,SAAA,KAAA,CAAA,MAAA,MAAA,OAAA,IAAA,IAAA;AACA;AAEA,SAAA,8BAAAA,YAAA,UAAA;AACA,QAAA,gBAAA,aAAAA,YAAA,QAAA;AAEA,MAAA,CAAA,iBAAA,cAAA,WAAA,GAAA;AACA,WAAA;EACA;AAEA,aAAA,SAAA,eAAA;AACA,QAAA,MAAA,MAAA,QAAA,MAAA,MAAA,SAAA,KAAA;AACA,YAAA,OAAA,SAAA,KAAA;AACA,YAAA,eAAA,0BAAA,SAAA,UAAA,gBAAA,MAAA,YAAA,CAAA;AAEA,UAAA,SAAA,aAAA,cAAA;AACA,eAAA,UAAA,YAAA;MACA;AAEA,aAAA;QACA,UAAA,QAAA,EAAA,IACA;UACA;YACAA,WAAA,OAAA,WAAA,UAAA,MAAA,KAAA;YACA;cACA,UAAA;YACA;UACA;QACA;MACA;IACA;EACA;AAEA,SAAA;AACA;AAEA,SAAA,kBACA,QACA,UACA,UACA,WAAA,IACA;AACA,MAAA,CAAA,UAAA,OAAA,WAAA,GAAA;AACA,WAAA,CAAA,iBAAA,0BAAA,SAAA,UAAA,QAAA,IAAA,SAAA,UAAA,KAAA;EACA;AAEA,MAAA,cAAA;AACA,MAAA,UAAA;AACA,eAAA,UAAA,UAAA;AACA,YAAA,QAAA,OAAA;AACA,UAAA,OAAA;AAEA,YAAA,MAAA,OAAA;AACA,iBAAA,cAAA,aAAA,OAAA,UAAA,QAAA;QACA;AACA,cAAA,OAAA,MAAA;AAGA,YAAA,QAAA,CAAA,6BAAA,MAAA,MAAA,GAAA;AACA,gBAAA,UAAA,KAAA,CAAA,MAAA,OAAA,YAAA,YAAA,SAAA,CAAA,MAAA,MAAA,OAAA,IAAA,IAAA;AACA,wBAAA,UAAA,WAAA,IAAA,gBAAA,OAAA;AAGA,cAAA,UAAA,SAAA,QAAA,MAAA,UAAA,WAAA,OAAA,QAAA,GAAA;AACA;;;;cAIA,uBAAA,WAAA,MAAA,uBAAA,OAAA,QAAA;cAEA,CAAA,qBAAA,WAAA;cACA;AACA,qBAAA,EAAA,iBAAA,KAAA,YAAA,SAAA,OAAA;YACA;AAGA,gBAAA,6BAAA,aAAA,MAAA,GAAA;AACA,4BAAA,YAAA,MAAA,GAAA,EAAA;YACA;AAEA,mBAAA,EAAA,iBAAA,KAAA,YAAA,aAAA,OAAA;UACA;QACA;MACA;IACA;EACA;AAEA,QAAA,0BAAA,iBACA,0BAAA,SAAA,UAAA,QAAA,IACA,SAAA,YAAA;AAEA,SAAA,CAAA,yBAAA,KAAA;AACA;AAEA,SAAA,0BACA,gBACA,UACA,QACA,SACA,UACAA,YACA;AACA,QAAA,WAAA,MAAA,QAAA,OAAA,IACA,UACA,aAAAA,cAAA,QAAA,UAAA,QAAA;AAEA,MAAA,UAAA;AACA,QAAA,MACA,SAAA;AAEA,UAAA,sBAAA,gCAAA,UAAAA,cAAA,MAAA;AAEA,QAAA,qBAAA;AACA,aAAA,gBAAA,8BAAAA,cAAA,QAAA,QAAA,CAAA;AACA,eAAA;IACA;AAEA,QAAA,CAAA,uBAAA,CAAA,MAAA;AACA,OAAA,MAAA,MAAA,IAAA,kBAAA,QAAA,UAAA,UAAA,QAAA;IACA;AAEA,oBAAA,EAAA,mBAAA,QAAA,GAAA;AAEA,QAAA,gBAAA;AACA,qBAAA,WAAA,IAAA;AACA,qBAAA,aAAA,kCAAA,MAAA;IACA;EACA;AACA;AAGA,SAAA,+CACA,QACAL,UACA;AACA,MAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,sBAAA,CAAA,6BAAA,CAAA,cAAA;AACA,mBACA,OAAA,KAAA;mBACA,UAAA,kBAAA,YAAA,wBAAA,kBAAA;kCACA,yBAAA,kBAAA,YAAA,GAAA;AAEA,WAAA;EACA;AAEA,QAAA,eAAA,CAAA,UAAA;AACA,UAAA,oBAAA,cAAA,IAAA;AAEA,UAAA,WAAA,aAAA;AACA,UAAA,iBAAA,mBAAA;AAEA;MACA,MAAA;AACA,cAAA,SAAA,0BAAA,MAAA,QAAA;AAEA,YAAA,kBAAA,SAAA;AACA,+BAAA,MAAA;AAEA,oCAAAC,mBAAA,GAAA,UAAA,QAAA,QAAA,QAAA,MAAA,KAAA,SAAA,CAAA;AACA,4BAAA,UAAA;QACA,OAAA;AACA,2BAAA;YACA;YACA;YACA;YACA,SAAAD;YACA,WAAA,MAAA,KAAA,SAAA;UACA,CAAA;QACA;MACA;;;MAGA,CAAA,UAAA,cAAA;IACA;AAIA,WAAA,qBAAA,QAAA,EAAA,GAAA,MAAA,CAAA;EACA;AAEA,uBAAA,cAAA,MAAA;AAIA,SAAA;AACA;AAEA,SAAAC,qBAAA;AACA,QAAA,OAAA,cAAA;AACA,QAAA,WAAA,OAAA,YAAA,IAAA,IAAA;AAEA,MAAA,CAAA,UAAA;AACA,WAAA;EACA;AAEA,QAAA,KAAA,WAAA,QAAA,EAAA;AAGA,SAAA,OAAA,gBAAA,OAAA,aAAA,WAAA;AACA;AAKA,SAAA,uBAAA,KAAA;AAEA,SAAA,IAAA,MAAA,OAAA,EAAA,OAAA,OAAA,EAAA,SAAA,KAAA,MAAA,GAAA,EAAA;AACA;;;AC/qBrF,SAAS,uCACd,SACa;AACb,SAAO,gDAAgD,SAAS,GAAG;AACrE;AAMO,SAAS,gBAAgB,eAAqC;AACnE,SAAO,gCAAgC,eAAe,GAAG;AAC3D;AAMO,SAAS,0BAGd,sBAAoG;AACpG,SAAO,0CAA0C,sBAAsB,GAAG;AAC5E;AAQO,SAAS,yBAGd,4BAA0G;AAC1G,SAAO,yCAAyC,4BAA4B,GAAG;AACjF;AAOO,SAAS,+BAAqF,QAAc;AACjH,SAAO,+CAAqD,QAAQ,GAAG;AACzE;;;AC5CO,SAAS,uCACd,SACa;AACb,SAAO,gDAAgD,SAAS,GAAG;AACrE;AAOO,SAAS,+BAAqF,QAAc;AACjH,SAAO,+CAAqD,QAAQ,GAAG;AACzE;AAMO,SAAS,0BAGd,sBAAoG;AACpG,SAAO,0CAA0C,sBAAsB,GAAG;AAC5E;AAQO,SAAS,yBAGd,4BAA0G;AAC1G,SAAO,yCAAyC,4BAA4B,GAAG;AACjF;AAMO,SAAS,gBAAgB,eAAqC;AACnE,SAAO,gCAAgC,eAAe,GAAG;AAC3D;", "names": ["Profiler", "hoistNonReactStatics", "init", "browserInit", "error", "cause", "UNKNOWN_COMPONENT", "reducer", "options", "originalBrowserTracingIntegration", "onResolvedMatchedRoutes", "allRoutes", "normalizeTransactionName", "version", "getActiveRootSpan", "location", "useEffect", "matchRoutes", "allRoutes"]}