import { useEffect, useState } from "react";
import EmptyTalentSpace from "./EmptyTalentSpace";
import LockedTalent from "./LockedTalent";
import MainTalent from "./MainTalent";

export default function DisplayTalent({
    talent,
    selectedTalent,
    setSelectedTalent,
    isTalentDisabled,
    showEmptySpaces,
    unlockedTalents,
}) {
    const [unlockedTalent, setUnlockedTalent] = useState();

    const talentUnlocked = () => {
        const unlockedTalent = unlockedTalents?.find((t) => t?.talentId === talent?.id);
        return unlockedTalent || null;
    };

    useEffect(() => {
        setUnlockedTalent(talentUnlocked());
    }, [unlockedTalents, talent?.id]);

    if (talent?.talentType === "locked")
        return <LockedTalent talent={talent} selectedTalent={selectedTalent} setSelectedTalent={setSelectedTalent} />;

    if (talent?.talentType === "empty") {
        if (showEmptySpaces) {
            return <EmptyTalentSpace />;
        } else {
            return (
                <LockedTalent
                    talent={talent}
                    selectedTalent={selectedTalent}
                    setSelectedTalent={setSelectedTalent}
                    isTalentDisabled={isTalentDisabled}
                />
            );
        }
    }

    if (talent?.talentType === "passive" || talent?.talentType === "ability")
        return (
            <MainTalent
                talent={talent}
                selectedTalent={selectedTalent}
                setSelectedTalent={setSelectedTalent}
                isTalentDisabled={isTalentDisabled}
                talentUnlocked={unlockedTalent}
            />
        );

    return null;
}
