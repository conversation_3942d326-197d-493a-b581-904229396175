import {
  BrowserClient,
  OpenFeatureIntegrationHook,
  SDK_VERSION,
  SEMANTIC_ATTRIBUTE_SENTRY_OP,
  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,
  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,
  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,
  Scope,
  WINDOW,
  addBreadcrumb,
  addEventProcessor,
  addIntegration,
  addNonEnumerableProperty,
  applySdkMetadata,
  breadcrumbsIntegration,
  browserApiErrorsIntegration,
  browserProfilingIntegration,
  browserSessionIntegration,
  browserTracingIntegration,
  buildLaunchDarklyFlagUsedHandler,
  captureConsoleIntegration,
  captureEvent,
  captureException,
  captureFeedback,
  captureMessage,
  captureSession,
  chromeStackLineParser,
  close,
  consoleLoggingIntegration,
  contextLinesIntegration,
  continueTrace,
  createTransport,
  createUserFeedbackEnvelope,
  dedupeIntegration,
  defaultRequestInstrumentationOptions,
  defaultStackLineParsers,
  defaultStackParser,
  diagnoseSdkConnectivity,
  endSession,
  eventFiltersIntegration,
  eventFromException,
  eventFromMessage,
  exceptionFromError,
  extraErrorDataIntegration,
  featureFlagsIntegration,
  feedbackAsyncIntegration,
  feedbackSyncIntegration,
  flush,
  forceLoad,
  functionToStringIntegration,
  geckoStackLineParser,
  getActiveSpan,
  getClient,
  getCurrentScope,
  getDefaultIntegrations,
  getFeedback,
  getGlobalScope,
  getIsolationScope,
  getReplay,
  getRootSpan,
  getSpanDescendants,
  getSpanStatusFromHttpCode,
  getTraceData,
  globalHandlersIntegration,
  graphqlClientIntegration,
  httpClientIntegration,
  httpContextIntegration,
  inboundFiltersIntegration,
  init,
  instrumentOutgoingRequests,
  instrumentSupabaseClient,
  isEnabled,
  isError,
  isInitialized,
  lastEventId,
  launchDarklyIntegration,
  lazyLoadIntegration,
  linkedErrorsIntegration,
  log_exports,
  logger,
  makeBrowserOfflineTransport,
  makeFetchTransport,
  makeMultiplexedTransport,
  moduleMetadataIntegration,
  onLoad,
  openFeatureIntegration,
  opera10StackLineParser,
  opera11StackLineParser,
  parameterize,
  registerSpanErrorInstrumentation,
  replayCanvasIntegration,
  replayIntegration,
  reportingObserverIntegration,
  rewriteFramesIntegration,
  sendFeedback,
  setContext,
  setCurrentClient,
  setExtra,
  setExtras,
  setHttpStatus,
  setMeasurement,
  setTag,
  setTags,
  setUser,
  showReportDialog,
  spanToBaggageHeader,
  spanToJSON,
  spanToTraceHeader,
  spotlightBrowserIntegration,
  startBrowserTracingNavigationSpan,
  startBrowserTracingPageLoadSpan,
  startInactiveSpan,
  startNewTrace,
  startSession,
  startSpan,
  startSpanManual,
  statsigIntegration,
  supabaseIntegration,
  suppressTracing,
  thirdPartyErrorFilterIntegration,
  timestampInSeconds,
  unleashIntegration,
  updateSpanName,
  winjsStackLineParser,
  withActiveSpan,
  withIsolationScope,
  withScope,
  zodErrorsIntegration
} from "./chunk-DV2N5EES.js";
import {
  require_react
} from "./chunk-YUJ2LLIH.js";
import {
  __commonJS,
  __toESM
} from "./chunk-G3PMV62Z.js";

// ../node_modules/hoist-non-react-statics/node_modules/react-is/cjs/react-is.development.js
var require_react_is_development = __commonJS({
  "../node_modules/hoist-non-react-statics/node_modules/react-is/cjs/react-is.development.js"(exports) {
    "use strict";
    if (true) {
      (function() {
        "use strict";
        var hasSymbol = typeof Symbol === "function" && Symbol.for;
        var REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for("react.element") : 60103;
        var REACT_PORTAL_TYPE = hasSymbol ? Symbol.for("react.portal") : 60106;
        var REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for("react.fragment") : 60107;
        var REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for("react.strict_mode") : 60108;
        var REACT_PROFILER_TYPE = hasSymbol ? Symbol.for("react.profiler") : 60114;
        var REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for("react.provider") : 60109;
        var REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for("react.context") : 60110;
        var REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for("react.async_mode") : 60111;
        var REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for("react.concurrent_mode") : 60111;
        var REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for("react.forward_ref") : 60112;
        var REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for("react.suspense") : 60113;
        var REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for("react.suspense_list") : 60120;
        var REACT_MEMO_TYPE = hasSymbol ? Symbol.for("react.memo") : 60115;
        var REACT_LAZY_TYPE = hasSymbol ? Symbol.for("react.lazy") : 60116;
        var REACT_BLOCK_TYPE = hasSymbol ? Symbol.for("react.block") : 60121;
        var REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for("react.fundamental") : 60117;
        var REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for("react.responder") : 60118;
        var REACT_SCOPE_TYPE = hasSymbol ? Symbol.for("react.scope") : 60119;
        function isValidElementType(type) {
          return typeof type === "string" || typeof type === "function" || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
          type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === "object" && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
        }
        function typeOf(object) {
          if (typeof object === "object" && object !== null) {
            var $$typeof = object.$$typeof;
            switch ($$typeof) {
              case REACT_ELEMENT_TYPE:
                var type = object.type;
                switch (type) {
                  case REACT_ASYNC_MODE_TYPE:
                  case REACT_CONCURRENT_MODE_TYPE:
                  case REACT_FRAGMENT_TYPE:
                  case REACT_PROFILER_TYPE:
                  case REACT_STRICT_MODE_TYPE:
                  case REACT_SUSPENSE_TYPE:
                    return type;
                  default:
                    var $$typeofType = type && type.$$typeof;
                    switch ($$typeofType) {
                      case REACT_CONTEXT_TYPE:
                      case REACT_FORWARD_REF_TYPE:
                      case REACT_LAZY_TYPE:
                      case REACT_MEMO_TYPE:
                      case REACT_PROVIDER_TYPE:
                        return $$typeofType;
                      default:
                        return $$typeof;
                    }
                }
              case REACT_PORTAL_TYPE:
                return $$typeof;
            }
          }
          return void 0;
        }
        var AsyncMode = REACT_ASYNC_MODE_TYPE;
        var ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;
        var ContextConsumer = REACT_CONTEXT_TYPE;
        var ContextProvider = REACT_PROVIDER_TYPE;
        var Element = REACT_ELEMENT_TYPE;
        var ForwardRef = REACT_FORWARD_REF_TYPE;
        var Fragment = REACT_FRAGMENT_TYPE;
        var Lazy = REACT_LAZY_TYPE;
        var Memo = REACT_MEMO_TYPE;
        var Portal = REACT_PORTAL_TYPE;
        var Profiler2 = REACT_PROFILER_TYPE;
        var StrictMode = REACT_STRICT_MODE_TYPE;
        var Suspense = REACT_SUSPENSE_TYPE;
        var hasWarnedAboutDeprecatedIsAsyncMode = false;
        function isAsyncMode(object) {
          {
            if (!hasWarnedAboutDeprecatedIsAsyncMode) {
              hasWarnedAboutDeprecatedIsAsyncMode = true;
              console["warn"]("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.");
            }
          }
          return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
        }
        function isConcurrentMode(object) {
          return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
        }
        function isContextConsumer(object) {
          return typeOf(object) === REACT_CONTEXT_TYPE;
        }
        function isContextProvider(object) {
          return typeOf(object) === REACT_PROVIDER_TYPE;
        }
        function isElement(object) {
          return typeof object === "object" && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
        }
        function isForwardRef(object) {
          return typeOf(object) === REACT_FORWARD_REF_TYPE;
        }
        function isFragment(object) {
          return typeOf(object) === REACT_FRAGMENT_TYPE;
        }
        function isLazy(object) {
          return typeOf(object) === REACT_LAZY_TYPE;
        }
        function isMemo(object) {
          return typeOf(object) === REACT_MEMO_TYPE;
        }
        function isPortal(object) {
          return typeOf(object) === REACT_PORTAL_TYPE;
        }
        function isProfiler(object) {
          return typeOf(object) === REACT_PROFILER_TYPE;
        }
        function isStrictMode(object) {
          return typeOf(object) === REACT_STRICT_MODE_TYPE;
        }
        function isSuspense(object) {
          return typeOf(object) === REACT_SUSPENSE_TYPE;
        }
        exports.AsyncMode = AsyncMode;
        exports.ConcurrentMode = ConcurrentMode;
        exports.ContextConsumer = ContextConsumer;
        exports.ContextProvider = ContextProvider;
        exports.Element = Element;
        exports.ForwardRef = ForwardRef;
        exports.Fragment = Fragment;
        exports.Lazy = Lazy;
        exports.Memo = Memo;
        exports.Portal = Portal;
        exports.Profiler = Profiler2;
        exports.StrictMode = StrictMode;
        exports.Suspense = Suspense;
        exports.isAsyncMode = isAsyncMode;
        exports.isConcurrentMode = isConcurrentMode;
        exports.isContextConsumer = isContextConsumer;
        exports.isContextProvider = isContextProvider;
        exports.isElement = isElement;
        exports.isForwardRef = isForwardRef;
        exports.isFragment = isFragment;
        exports.isLazy = isLazy;
        exports.isMemo = isMemo;
        exports.isPortal = isPortal;
        exports.isProfiler = isProfiler;
        exports.isStrictMode = isStrictMode;
        exports.isSuspense = isSuspense;
        exports.isValidElementType = isValidElementType;
        exports.typeOf = typeOf;
      })();
    }
  }
});

// ../node_modules/hoist-non-react-statics/node_modules/react-is/index.js
var require_react_is = __commonJS({
  "../node_modules/hoist-non-react-statics/node_modules/react-is/index.js"(exports, module) {
    "use strict";
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_react_is_development();
    }
  }
});

// ../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js
var require_hoist_non_react_statics_cjs = __commonJS({
  "../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js"(exports, module) {
    "use strict";
    var reactIs = require_react_is();
    var REACT_STATICS = {
      childContextTypes: true,
      contextType: true,
      contextTypes: true,
      defaultProps: true,
      displayName: true,
      getDefaultProps: true,
      getDerivedStateFromError: true,
      getDerivedStateFromProps: true,
      mixins: true,
      propTypes: true,
      type: true
    };
    var KNOWN_STATICS = {
      name: true,
      length: true,
      prototype: true,
      caller: true,
      callee: true,
      arguments: true,
      arity: true
    };
    var FORWARD_REF_STATICS = {
      "$$typeof": true,
      render: true,
      defaultProps: true,
      displayName: true,
      propTypes: true
    };
    var MEMO_STATICS = {
      "$$typeof": true,
      compare: true,
      defaultProps: true,
      displayName: true,
      propTypes: true,
      type: true
    };
    var TYPE_STATICS = {};
    TYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;
    TYPE_STATICS[reactIs.Memo] = MEMO_STATICS;
    function getStatics(component) {
      if (reactIs.isMemo(component)) {
        return MEMO_STATICS;
      }
      return TYPE_STATICS[component["$$typeof"]] || REACT_STATICS;
    }
    var defineProperty = Object.defineProperty;
    var getOwnPropertyNames = Object.getOwnPropertyNames;
    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
    var getPrototypeOf = Object.getPrototypeOf;
    var objectPrototype = Object.prototype;
    function hoistNonReactStatics2(targetComponent, sourceComponent, blacklist) {
      if (typeof sourceComponent !== "string") {
        if (objectPrototype) {
          var inheritedComponent = getPrototypeOf(sourceComponent);
          if (inheritedComponent && inheritedComponent !== objectPrototype) {
            hoistNonReactStatics2(targetComponent, inheritedComponent, blacklist);
          }
        }
        var keys = getOwnPropertyNames(sourceComponent);
        if (getOwnPropertySymbols) {
          keys = keys.concat(getOwnPropertySymbols(sourceComponent));
        }
        var targetStatics = getStatics(targetComponent);
        var sourceStatics = getStatics(sourceComponent);
        for (var i = 0; i < keys.length; ++i) {
          var key = keys[i];
          if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {
            var descriptor = getOwnPropertyDescriptor(sourceComponent, key);
            try {
              defineProperty(targetComponent, key, descriptor);
            } catch (e) {
            }
          }
        }
      }
      return targetComponent;
    }
    module.exports = hoistNonReactStatics2;
  }
});

// ../node_modules/@sentry/react/build/esm/sdk.js
var import_react = __toESM(require_react(), 1);
function init2(options) {
  const opts = {
    ...options
  };
  applySdkMetadata(opts, "react");
  setContext("react", { version: import_react.version });
  return init(opts);
}

// ../node_modules/@sentry/react/build/esm/error.js
var import_react2 = __toESM(require_react(), 1);
function isAtLeastReact17(reactVersion) {
  const reactMajor = reactVersion.match(/^([^.]+)/);
  return reactMajor !== null && parseInt(reactMajor[0]) >= 17;
}
function setCause(error, cause) {
  const seenErrors = /* @__PURE__ */ new WeakSet();
  function recurse(error2, cause2) {
    if (seenErrors.has(error2)) {
      return;
    }
    if (error2.cause) {
      seenErrors.add(error2);
      return recurse(error2.cause, cause2);
    }
    error2.cause = cause2;
  }
  recurse(error, cause);
}
function captureReactException(error, { componentStack }, hint) {
  if (isAtLeastReact17(import_react2.version) && isError(error) && componentStack) {
    const errorBoundaryError = new Error(error.message);
    errorBoundaryError.name = `React ErrorBoundary ${error.name}`;
    errorBoundaryError.stack = componentStack;
    setCause(error, errorBoundaryError);
  }
  return withScope((scope) => {
    scope.setContext("react", { componentStack });
    return captureException(error, hint);
  });
}
function reactErrorHandler(callback) {
  return (error, errorInfo) => {
    const eventId = captureReactException(error, errorInfo);
    if (callback) {
      callback(error, errorInfo, eventId);
    }
  };
}

// ../node_modules/@sentry/react/build/esm/profiler.js
var React = __toESM(require_react(), 1);

// ../node_modules/@sentry/react/build/esm/constants.js
var REACT_RENDER_OP = "ui.react.render";
var REACT_UPDATE_OP = "ui.react.update";
var REACT_MOUNT_OP = "ui.react.mount";

// ../node_modules/@sentry/react/build/esm/hoist-non-react-statics.js
var hoistNonReactStaticsImport = __toESM(require_hoist_non_react_statics_cjs(), 1);
var hoistNonReactStatics = hoistNonReactStaticsImport.default || hoistNonReactStaticsImport;

// ../node_modules/@sentry/react/build/esm/profiler.js
var UNKNOWN_COMPONENT = "unknown";
var Profiler = class extends React.Component {
  /**
   * The span of the mount activity
   * Made protected for the React Native SDK to access
   */
  /**
   * The span that represents the duration of time between shouldComponentUpdate and componentDidUpdate
   */
  constructor(props) {
    super(props);
    const { name, disabled = false } = this.props;
    if (disabled) {
      return;
    }
    this._mountSpan = startInactiveSpan({
      name: `<${name}>`,
      onlyIfParent: true,
      op: REACT_MOUNT_OP,
      attributes: {
        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.ui.react.profiler",
        "ui.component_name": name
      }
    });
  }
  // If a component mounted, we can finish the mount activity.
  componentDidMount() {
    if (this._mountSpan) {
      this._mountSpan.end();
    }
  }
  shouldComponentUpdate({ updateProps, includeUpdates = true }) {
    if (includeUpdates && this._mountSpan && updateProps !== this.props.updateProps) {
      const changedProps = Object.keys(updateProps).filter((k) => updateProps[k] !== this.props.updateProps[k]);
      if (changedProps.length > 0) {
        const now = timestampInSeconds();
        this._updateSpan = withActiveSpan(this._mountSpan, () => {
          return startInactiveSpan({
            name: `<${this.props.name}>`,
            onlyIfParent: true,
            op: REACT_UPDATE_OP,
            startTime: now,
            attributes: {
              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.ui.react.profiler",
              "ui.component_name": this.props.name,
              "ui.react.changed_props": changedProps
            }
          });
        });
      }
    }
    return true;
  }
  componentDidUpdate() {
    if (this._updateSpan) {
      this._updateSpan.end();
      this._updateSpan = void 0;
    }
  }
  // If a component is unmounted, we can say it is no longer on the screen.
  // This means we can finish the span representing the component render.
  componentWillUnmount() {
    const endTimestamp = timestampInSeconds();
    const { name, includeRender = true } = this.props;
    if (this._mountSpan && includeRender) {
      const startTime = spanToJSON(this._mountSpan).timestamp;
      withActiveSpan(this._mountSpan, () => {
        const renderSpan = startInactiveSpan({
          onlyIfParent: true,
          name: `<${name}>`,
          op: REACT_RENDER_OP,
          startTime,
          attributes: {
            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.ui.react.profiler",
            "ui.component_name": name
          }
        });
        if (renderSpan) {
          renderSpan.end(endTimestamp);
        }
      });
    }
  }
  render() {
    return this.props.children;
  }
};
Object.assign(Profiler, {
  defaultProps: {
    disabled: false,
    includeRender: true,
    includeUpdates: true
  }
});
function withProfiler(WrappedComponent, options) {
  const componentDisplayName = options?.name || WrappedComponent.displayName || WrappedComponent.name || UNKNOWN_COMPONENT;
  const Wrapped = (props) => React.createElement(
    Profiler,
    { ...options, name: componentDisplayName, updateProps: props },
    React.createElement(WrappedComponent, { ...props })
  );
  Wrapped.displayName = `profiler(${componentDisplayName})`;
  hoistNonReactStatics(Wrapped, WrappedComponent);
  return Wrapped;
}
function useProfiler(name, options = {
  disabled: false,
  hasRenderSpan: true
}) {
  const [mountSpan] = React.useState(() => {
    if (options?.disabled) {
      return void 0;
    }
    return startInactiveSpan({
      name: `<${name}>`,
      onlyIfParent: true,
      op: REACT_MOUNT_OP,
      attributes: {
        [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.ui.react.profiler",
        "ui.component_name": name
      }
    });
  });
  React.useEffect(() => {
    if (mountSpan) {
      mountSpan.end();
    }
    return () => {
      if (mountSpan && options.hasRenderSpan) {
        const startTime = spanToJSON(mountSpan).timestamp;
        const endTimestamp = timestampInSeconds();
        const renderSpan = startInactiveSpan({
          name: `<${name}>`,
          onlyIfParent: true,
          op: REACT_RENDER_OP,
          startTime,
          attributes: {
            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.ui.react.profiler",
            "ui.component_name": name
          }
        });
        if (renderSpan) {
          renderSpan.end(endTimestamp);
        }
      }
    };
  }, []);
}

// ../node_modules/@sentry/react/build/esm/errorboundary.js
var React2 = __toESM(require_react(), 1);

// ../node_modules/@sentry/react/build/esm/debug-build.js
var DEBUG_BUILD = typeof __SENTRY_DEBUG__ === "undefined" || __SENTRY_DEBUG__;

// ../node_modules/@sentry/react/build/esm/errorboundary.js
var UNKNOWN_COMPONENT2 = "unknown";
var INITIAL_STATE = {
  componentStack: null,
  error: null,
  eventId: null
};
var ErrorBoundary = class extends React2.Component {
  constructor(props) {
    super(props);
    this.state = INITIAL_STATE;
    this._openFallbackReportDialog = true;
    const client = getClient();
    if (client && props.showDialog) {
      this._openFallbackReportDialog = false;
      this._cleanupHook = client.on("afterSendEvent", (event) => {
        if (!event.type && this._lastEventId && event.event_id === this._lastEventId) {
          showReportDialog({ ...props.dialogOptions, eventId: this._lastEventId });
        }
      });
    }
  }
  componentDidCatch(error, errorInfo) {
    const { componentStack } = errorInfo;
    const { beforeCapture, onError, showDialog, dialogOptions } = this.props;
    withScope((scope) => {
      if (beforeCapture) {
        beforeCapture(scope, error, componentStack);
      }
      const handled = this.props.handled != null ? this.props.handled : !!this.props.fallback;
      const eventId = captureReactException(error, errorInfo, { mechanism: { handled } });
      if (onError) {
        onError(error, componentStack, eventId);
      }
      if (showDialog) {
        this._lastEventId = eventId;
        if (this._openFallbackReportDialog) {
          showReportDialog({ ...dialogOptions, eventId });
        }
      }
      this.setState({ error, componentStack, eventId });
    });
  }
  componentDidMount() {
    const { onMount } = this.props;
    if (onMount) {
      onMount();
    }
  }
  componentWillUnmount() {
    const { error, componentStack, eventId } = this.state;
    const { onUnmount } = this.props;
    if (onUnmount) {
      if (this.state === INITIAL_STATE) {
        onUnmount(null, null, null);
      } else {
        onUnmount(error, componentStack, eventId);
      }
    }
    if (this._cleanupHook) {
      this._cleanupHook();
      this._cleanupHook = void 0;
    }
  }
  resetErrorBoundary() {
    const { onReset } = this.props;
    const { error, componentStack, eventId } = this.state;
    if (onReset) {
      onReset(error, componentStack, eventId);
    }
    this.setState(INITIAL_STATE);
  }
  render() {
    const { fallback, children } = this.props;
    const state = this.state;
    if (state.componentStack === null) {
      return typeof children === "function" ? children() : children;
    }
    const element = typeof fallback === "function" ? React2.createElement(fallback, {
      error: state.error,
      componentStack: state.componentStack,
      resetError: () => this.resetErrorBoundary(),
      eventId: state.eventId
    }) : fallback;
    if (React2.isValidElement(element)) {
      return element;
    }
    if (fallback) {
      DEBUG_BUILD && logger.warn("fallback did not produce a valid ReactElement");
    }
    return null;
  }
};
function withErrorBoundary(WrappedComponent, errorBoundaryOptions) {
  const componentDisplayName = WrappedComponent.displayName || WrappedComponent.name || UNKNOWN_COMPONENT2;
  const Wrapped = (props) => React2.createElement(
    ErrorBoundary,
    { ...errorBoundaryOptions },
    React2.createElement(WrappedComponent, { ...props })
  );
  Wrapped.displayName = `errorBoundary(${componentDisplayName})`;
  hoistNonReactStatics(Wrapped, WrappedComponent);
  return Wrapped;
}

// ../node_modules/@sentry/react/build/esm/redux.js
var ACTION_BREADCRUMB_CATEGORY = "redux.action";
var ACTION_BREADCRUMB_TYPE = "info";
var defaultOptions = {
  attachReduxState: true,
  actionTransformer: (action) => action,
  stateTransformer: (state) => state || null
};
function createReduxEnhancer(enhancerOptions) {
  const options = {
    ...defaultOptions,
    ...enhancerOptions
  };
  return (next) => (reducer, initialState) => {
    options.attachReduxState && getGlobalScope().addEventProcessor((event, hint) => {
      try {
        if (event.type === void 0 && event.contexts.state.state.type === "redux") {
          hint.attachments = [
            ...hint.attachments || [],
            // @ts-expect-error try catch to reduce bundle size
            { filename: "redux_state.json", data: JSON.stringify(event.contexts.state.state.value) }
          ];
        }
      } catch (_) {
      }
      return event;
    });
    function sentryWrapReducer(reducer2) {
      return (state, action) => {
        const newState = reducer2(state, action);
        const scope = getCurrentScope();
        const transformedAction = options.actionTransformer(action);
        if (typeof transformedAction !== "undefined" && transformedAction !== null) {
          addBreadcrumb({
            category: ACTION_BREADCRUMB_CATEGORY,
            data: transformedAction,
            type: ACTION_BREADCRUMB_TYPE
          });
        }
        const transformedState = options.stateTransformer(newState);
        if (typeof transformedState !== "undefined" && transformedState !== null) {
          const client = getClient();
          const options2 = client?.getOptions();
          const normalizationDepth = options2?.normalizeDepth || 3;
          const newStateContext = { state: { type: "redux", value: transformedState } };
          addNonEnumerableProperty(
            newStateContext,
            "__sentry_override_normalization_depth__",
            3 + // 3 layers for `state.value.transformedState`
            normalizationDepth
            // rest for the actual state
          );
          scope.setContext("state", newStateContext);
        } else {
          scope.setContext("state", null);
        }
        const { configureScopeWithState } = options;
        if (typeof configureScopeWithState === "function") {
          configureScopeWithState(scope, newState);
        }
        return newState;
      };
    }
    const store = next(sentryWrapReducer(reducer), initialState);
    store.replaceReducer = new Proxy(store.replaceReducer, {
      apply: function(target, thisArg, args) {
        target.apply(thisArg, [sentryWrapReducer(args[0])]);
      }
    });
    return store;
  };
}

// ../node_modules/@sentry/react/build/esm/reactrouterv3.js
function reactRouterV3BrowserTracingIntegration(options) {
  const integration = browserTracingIntegration({
    ...options,
    instrumentPageLoad: false,
    instrumentNavigation: false
  });
  const { history, routes, match, instrumentPageLoad = true, instrumentNavigation = true } = options;
  return {
    ...integration,
    afterAllSetup(client) {
      integration.afterAllSetup(client);
      if (instrumentPageLoad && WINDOW.location) {
        normalizeTransactionName(
          routes,
          WINDOW.location,
          match,
          (localName, source = "url") => {
            startBrowserTracingPageLoadSpan(client, {
              name: localName,
              attributes: {
                [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "pageload",
                [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.pageload.react.reactrouter_v3",
                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source
              }
            });
          }
        );
      }
      if (instrumentNavigation && history.listen) {
        history.listen((location) => {
          if (location.action === "PUSH" || location.action === "POP") {
            normalizeTransactionName(
              routes,
              location,
              match,
              (localName, source = "url") => {
                startBrowserTracingNavigationSpan(client, {
                  name: localName,
                  attributes: {
                    [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "navigation",
                    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.navigation.react.reactrouter_v3",
                    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source
                  }
                });
              }
            );
          }
        });
      }
    }
  };
}
function normalizeTransactionName(appRoutes, location, match, callback) {
  let name = location.pathname;
  match(
    {
      location,
      routes: appRoutes
    },
    (error, _redirectLocation, renderProps) => {
      if (error || !renderProps) {
        return callback(name);
      }
      const routePath = getRouteStringFromRoutes(renderProps.routes || []);
      if (routePath.length === 0 || routePath === "/*") {
        return callback(name);
      }
      name = routePath;
      return callback(name, "route");
    }
  );
}
function getRouteStringFromRoutes(routes) {
  if (!Array.isArray(routes) || routes.length === 0) {
    return "";
  }
  const routesWithPaths = routes.filter((route) => !!route.path);
  let index = -1;
  for (let x = routesWithPaths.length - 1; x >= 0; x--) {
    const route = routesWithPaths[x];
    if (route.path?.startsWith("/")) {
      index = x;
      break;
    }
  }
  return routesWithPaths.slice(index).reduce((acc, { path }) => {
    const pathSegment = acc === "/" || acc === "" ? path : `/${path}`;
    return `${acc}${pathSegment}`;
  }, "");
}

// ../node_modules/@sentry/react/build/esm/tanstackrouter.js
function tanstackRouterBrowserTracingIntegration(router, options = {}) {
  const castRouterInstance = router;
  const browserTracingIntegrationInstance = browserTracingIntegration({
    ...options,
    instrumentNavigation: false,
    instrumentPageLoad: false
  });
  const { instrumentPageLoad = true, instrumentNavigation = true } = options;
  return {
    ...browserTracingIntegrationInstance,
    afterAllSetup(client) {
      browserTracingIntegrationInstance.afterAllSetup(client);
      const initialWindowLocation = WINDOW.location;
      if (instrumentPageLoad && initialWindowLocation) {
        const matchedRoutes = castRouterInstance.matchRoutes(
          initialWindowLocation.pathname,
          castRouterInstance.options.parseSearch(initialWindowLocation.search),
          { preload: false, throwOnError: false }
        );
        const lastMatch = matchedRoutes[matchedRoutes.length - 1];
        startBrowserTracingPageLoadSpan(client, {
          name: lastMatch ? lastMatch.routeId : initialWindowLocation.pathname,
          attributes: {
            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "pageload",
            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.pageload.react.tanstack_router",
            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: lastMatch ? "route" : "url",
            ...routeMatchToParamSpanAttributes(lastMatch)
          }
        });
      }
      if (instrumentNavigation) {
        castRouterInstance.subscribe("onBeforeNavigate", (onBeforeNavigateArgs) => {
          if (onBeforeNavigateArgs.toLocation.state === onBeforeNavigateArgs.fromLocation?.state) {
            return;
          }
          const onResolvedMatchedRoutes = castRouterInstance.matchRoutes(
            onBeforeNavigateArgs.toLocation.pathname,
            onBeforeNavigateArgs.toLocation.search,
            { preload: false, throwOnError: false }
          );
          const onBeforeNavigateLastMatch = onResolvedMatchedRoutes[onResolvedMatchedRoutes.length - 1];
          const navigationLocation = WINDOW.location;
          const navigationSpan = startBrowserTracingNavigationSpan(client, {
            name: onBeforeNavigateLastMatch ? onBeforeNavigateLastMatch.routeId : navigationLocation.pathname,
            attributes: {
              [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "navigation",
              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: "auto.navigation.react.tanstack_router",
              [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: onBeforeNavigateLastMatch ? "route" : "url"
            }
          });
          const unsubscribeOnResolved = castRouterInstance.subscribe("onResolved", (onResolvedArgs) => {
            unsubscribeOnResolved();
            if (navigationSpan) {
              const onResolvedMatchedRoutes2 = castRouterInstance.matchRoutes(
                onResolvedArgs.toLocation.pathname,
                onResolvedArgs.toLocation.search,
                { preload: false, throwOnError: false }
              );
              const onResolvedLastMatch = onResolvedMatchedRoutes2[onResolvedMatchedRoutes2.length - 1];
              if (onResolvedLastMatch) {
                navigationSpan.updateName(onResolvedLastMatch.routeId);
                navigationSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, "route");
                navigationSpan.setAttributes(routeMatchToParamSpanAttributes(onResolvedLastMatch));
              }
            }
          });
        });
      }
    }
  };
}
function routeMatchToParamSpanAttributes(match) {
  if (!match) {
    return {};
  }
  const paramAttributes = {};
  Object.entries(match.params).forEach(([key, value]) => {
    paramAttributes[`url.path.params.${key}`] = value;
    paramAttributes[`url.path.parameter.${key}`] = value;
    paramAttributes[`params.${key}`] = value;
  });
  return paramAttributes;
}

// ../node_modules/@sentry/react/build/esm/reactrouter.js
var React3 = __toESM(require_react(), 1);
function reactRouterV4BrowserTracingIntegration(options) {
  const integration = browserTracingIntegration({
    ...options,
    instrumentPageLoad: false,
    instrumentNavigation: false
  });
  const { history, routes, matchPath, instrumentPageLoad = true, instrumentNavigation = true } = options;
  return {
    ...integration,
    afterAllSetup(client) {
      integration.afterAllSetup(client);
      instrumentReactRouter(
        client,
        instrumentPageLoad,
        instrumentNavigation,
        history,
        "reactrouter_v4",
        routes,
        matchPath
      );
    }
  };
}
function reactRouterV5BrowserTracingIntegration(options) {
  const integration = browserTracingIntegration({
    ...options,
    instrumentPageLoad: false,
    instrumentNavigation: false
  });
  const { history, routes, matchPath, instrumentPageLoad = true, instrumentNavigation = true } = options;
  return {
    ...integration,
    afterAllSetup(client) {
      integration.afterAllSetup(client);
      instrumentReactRouter(
        client,
        instrumentPageLoad,
        instrumentNavigation,
        history,
        "reactrouter_v5",
        routes,
        matchPath
      );
    }
  };
}
function instrumentReactRouter(client, instrumentPageLoad, instrumentNavigation, history, instrumentationName, allRoutes2 = [], matchPath) {
  function getInitPathName() {
    if (history.location) {
      return history.location.pathname;
    }
    if (WINDOW.location) {
      return WINDOW.location.pathname;
    }
    return void 0;
  }
  function normalizeTransactionName2(pathname) {
    if (allRoutes2.length === 0 || !matchPath) {
      return [pathname, "url"];
    }
    const branches = matchRoutes(allRoutes2, pathname, matchPath);
    for (const branch of branches) {
      if (branch.match.isExact) {
        return [branch.match.path, "route"];
      }
    }
    return [pathname, "url"];
  }
  if (instrumentPageLoad) {
    const initPathName = getInitPathName();
    if (initPathName) {
      const [name, source] = normalizeTransactionName2(initPathName);
      startBrowserTracingPageLoadSpan(client, {
        name,
        attributes: {
          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "pageload",
          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.pageload.react.${instrumentationName}`,
          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source
        }
      });
    }
  }
  if (instrumentNavigation && history.listen) {
    history.listen((location, action) => {
      if (action && (action === "PUSH" || action === "POP")) {
        const [name, source] = normalizeTransactionName2(location.pathname);
        startBrowserTracingNavigationSpan(client, {
          name,
          attributes: {
            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "navigation",
            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.navigation.react.${instrumentationName}`,
            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source
          }
        });
      }
    });
  }
}
function matchRoutes(routes, pathname, matchPath, branch = []) {
  routes.some((route) => {
    const match = route.path ? matchPath(pathname, route) : branch.length ? (
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      branch[branch.length - 1].match
    ) : computeRootMatch(pathname);
    if (match) {
      branch.push({ route, match });
      if (route.routes) {
        matchRoutes(route.routes, pathname, matchPath, branch);
      }
    }
    return !!match;
  });
  return branch;
}
function computeRootMatch(pathname) {
  return { path: "/", url: "/", params: {}, isExact: pathname === "/" };
}
function withSentryRouting(Route) {
  const componentDisplayName = Route.displayName || Route.name;
  const WrappedRoute = (props) => {
    if (props?.computedMatch?.isExact) {
      const route = props.computedMatch.path;
      const activeRootSpan = getActiveRootSpan();
      getCurrentScope().setTransactionName(route);
      if (activeRootSpan) {
        activeRootSpan.updateName(route);
        activeRootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, "route");
      }
    }
    return React3.createElement(Route, { ...props });
  };
  WrappedRoute.displayName = `sentryRoute(${componentDisplayName})`;
  hoistNonReactStatics(WrappedRoute, Route);
  return WrappedRoute;
}
function getActiveRootSpan() {
  const span = getActiveSpan();
  const rootSpan = span && getRootSpan(span);
  if (!rootSpan) {
    return void 0;
  }
  const op = spanToJSON(rootSpan).op;
  return op === "navigation" || op === "pageload" ? rootSpan : void 0;
}

// ../node_modules/@sentry/react/build/esm/reactrouterv6-compat-utils.js
var React4 = __toESM(require_react(), 1);
var _useEffect;
var _useLocation;
var _useNavigationType;
var _createRoutesFromChildren;
var _matchRoutes;
var _stripBasename = false;
var CLIENTS_WITH_INSTRUMENT_NAVIGATION = /* @__PURE__ */ new WeakSet();
var allRoutes = /* @__PURE__ */ new Set();
function createV6CompatibleWrapCreateBrowserRouter(createRouterFunction, version3) {
  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {
    DEBUG_BUILD && logger.warn(
      `reactRouterV${version3}Instrumentation was unable to wrap the \`createRouter\` function because of one or more missing parameters.`
    );
    return createRouterFunction;
  }
  return function(routes, opts) {
    addRoutesToAllRoutes(routes);
    const router = createRouterFunction(routes, opts);
    const basename = opts?.basename;
    const activeRootSpan = getActiveRootSpan2();
    if (router.state.historyAction === "POP" && activeRootSpan) {
      updatePageloadTransaction(
        activeRootSpan,
        router.state.location,
        routes,
        void 0,
        basename,
        Array.from(allRoutes)
      );
    }
    router.subscribe((state) => {
      if (state.historyAction === "PUSH" || state.historyAction === "POP") {
        if (state.navigation.state !== "idle") {
          requestAnimationFrame(() => {
            handleNavigation({
              location: state.location,
              routes,
              navigationType: state.historyAction,
              version: version3,
              basename,
              allRoutes: Array.from(allRoutes)
            });
          });
        } else {
          handleNavigation({
            location: state.location,
            routes,
            navigationType: state.historyAction,
            version: version3,
            basename,
            allRoutes: Array.from(allRoutes)
          });
        }
      }
    });
    return router;
  };
}
function createV6CompatibleWrapCreateMemoryRouter(createRouterFunction, version3) {
  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {
    DEBUG_BUILD && logger.warn(
      `reactRouterV${version3}Instrumentation was unable to wrap the \`createMemoryRouter\` function because of one or more missing parameters.`
    );
    return createRouterFunction;
  }
  return function(routes, opts) {
    addRoutesToAllRoutes(routes);
    const router = createRouterFunction(routes, opts);
    const basename = opts?.basename;
    const activeRootSpan = getActiveRootSpan2();
    let initialEntry = void 0;
    const initialEntries = opts?.initialEntries;
    const initialIndex = opts?.initialIndex;
    const hasOnlyOneInitialEntry = initialEntries && initialEntries.length === 1;
    const hasIndexedEntry = initialIndex !== void 0 && initialEntries && initialEntries[initialIndex];
    initialEntry = hasOnlyOneInitialEntry ? initialEntries[0] : hasIndexedEntry ? initialEntries[initialIndex] : void 0;
    const location = initialEntry ? typeof initialEntry === "string" ? { pathname: initialEntry } : initialEntry : router.state.location;
    if (router.state.historyAction === "POP" && activeRootSpan) {
      updatePageloadTransaction(activeRootSpan, location, routes, void 0, basename, Array.from(allRoutes));
    }
    router.subscribe((state) => {
      const location2 = state.location;
      if (state.historyAction === "PUSH" || state.historyAction === "POP") {
        handleNavigation({
          location: location2,
          routes,
          navigationType: state.historyAction,
          version: version3,
          basename,
          allRoutes: Array.from(allRoutes)
        });
      }
    });
    return router;
  };
}
function createReactRouterV6CompatibleTracingIntegration(options, version3) {
  const integration = browserTracingIntegration({
    ...options,
    instrumentPageLoad: false,
    instrumentNavigation: false
  });
  const {
    useEffect: useEffect2,
    useLocation,
    useNavigationType,
    createRoutesFromChildren,
    matchRoutes: matchRoutes2,
    stripBasename,
    instrumentPageLoad = true,
    instrumentNavigation = true
  } = options;
  return {
    ...integration,
    setup(client) {
      integration.setup(client);
      _useEffect = useEffect2;
      _useLocation = useLocation;
      _useNavigationType = useNavigationType;
      _matchRoutes = matchRoutes2;
      _createRoutesFromChildren = createRoutesFromChildren;
      _stripBasename = stripBasename || false;
    },
    afterAllSetup(client) {
      integration.afterAllSetup(client);
      const initPathName = WINDOW.location?.pathname;
      if (instrumentPageLoad && initPathName) {
        startBrowserTracingPageLoadSpan(client, {
          name: initPathName,
          attributes: {
            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: "url",
            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "pageload",
            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.pageload.react.reactrouter_v${version3}`
          }
        });
      }
      if (instrumentNavigation) {
        CLIENTS_WITH_INSTRUMENT_NAVIGATION.add(client);
      }
    }
  };
}
function createV6CompatibleWrapUseRoutes(origUseRoutes, version3) {
  if (!_useEffect || !_useLocation || !_useNavigationType || !_matchRoutes) {
    DEBUG_BUILD && logger.warn(
      "reactRouterV6Instrumentation was unable to wrap `useRoutes` because of one or more missing parameters."
    );
    return origUseRoutes;
  }
  const SentryRoutes = (props) => {
    const isMountRenderPass = React4.useRef(true);
    const { routes, locationArg } = props;
    const Routes = origUseRoutes(routes, locationArg);
    const location = _useLocation();
    const navigationType = _useNavigationType();
    const stableLocationParam = typeof locationArg === "string" || locationArg?.pathname ? locationArg : location;
    _useEffect(() => {
      const normalizedLocation = typeof stableLocationParam === "string" ? { pathname: stableLocationParam } : stableLocationParam;
      if (isMountRenderPass.current) {
        addRoutesToAllRoutes(routes);
        updatePageloadTransaction(
          getActiveRootSpan2(),
          normalizedLocation,
          routes,
          void 0,
          void 0,
          Array.from(allRoutes)
        );
        isMountRenderPass.current = false;
      } else {
        handleNavigation({
          location: normalizedLocation,
          routes,
          navigationType,
          version: version3,
          allRoutes: Array.from(allRoutes)
        });
      }
    }, [navigationType, stableLocationParam]);
    return Routes;
  };
  return (routes, locationArg) => {
    return React4.createElement(SentryRoutes, { routes, locationArg });
  };
}
function handleNavigation(opts) {
  const { location, routes, navigationType, version: version3, matches, basename, allRoutes: allRoutes2 } = opts;
  const branches = Array.isArray(matches) ? matches : _matchRoutes(routes, location, basename);
  const client = getClient();
  if (!client || !CLIENTS_WITH_INSTRUMENT_NAVIGATION.has(client)) {
    return;
  }
  if ((navigationType === "PUSH" || navigationType === "POP") && branches) {
    let name, source = "url";
    const isInDescendantRoute = locationIsInsideDescendantRoute(location, allRoutes2 || routes);
    if (isInDescendantRoute) {
      name = prefixWithSlash(rebuildRoutePathFromAllRoutes(allRoutes2 || routes, location));
      source = "route";
    }
    if (!isInDescendantRoute || !name) {
      [name, source] = getNormalizedName(routes, location, branches, basename);
    }
    const activeSpan = getActiveSpan();
    const isAlreadyInNavigationSpan = activeSpan && spanToJSON(activeSpan).op === "navigation";
    if (isAlreadyInNavigationSpan) {
      activeSpan?.updateName(name);
      activeSpan?.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, source);
    } else {
      startBrowserTracingNavigationSpan(client, {
        name,
        attributes: {
          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: source,
          [SEMANTIC_ATTRIBUTE_SENTRY_OP]: "navigation",
          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: `auto.navigation.react.reactrouter_v${version3}`
        }
      });
    }
  }
}
function stripBasenameFromPathname(pathname, basename) {
  if (!basename || basename === "/") {
    return pathname;
  }
  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {
    return pathname;
  }
  const startIndex = basename.endsWith("/") ? basename.length - 1 : basename.length;
  const nextChar = pathname.charAt(startIndex);
  if (nextChar && nextChar !== "/") {
    return pathname;
  }
  return pathname.slice(startIndex) || "/";
}
function sendIndexPath(pathBuilder, pathname, basename) {
  const reconstructedPath = pathBuilder || _stripBasename ? stripBasenameFromPathname(pathname, basename) : pathname;
  const formattedPath = (
    // If the path ends with a slash, remove it
    reconstructedPath[reconstructedPath.length - 1] === "/" ? reconstructedPath.slice(0, -1) : (
      // If the path ends with a wildcard, remove it
      reconstructedPath.slice(-2) === "/*" ? reconstructedPath.slice(0, -1) : reconstructedPath
    )
  );
  return [formattedPath, "route"];
}
function pathEndsWithWildcard(path) {
  return path.endsWith("*");
}
function pathIsWildcardAndHasChildren(path, branch) {
  return pathEndsWithWildcard(path) && !!branch.route.children?.length || false;
}
function routeIsDescendant(route) {
  return !!(!route.children && route.element && route.path?.endsWith("/*"));
}
function locationIsInsideDescendantRoute(location, routes) {
  const matchedRoutes = _matchRoutes(routes, location);
  if (matchedRoutes) {
    for (const match of matchedRoutes) {
      if (routeIsDescendant(match.route) && pickSplat(match)) {
        return true;
      }
    }
  }
  return false;
}
function addRoutesToAllRoutes(routes) {
  routes.forEach((route) => {
    const extractedChildRoutes = getChildRoutesRecursively(route);
    extractedChildRoutes.forEach((r) => {
      allRoutes.add(r);
    });
  });
}
function getChildRoutesRecursively(route, allRoutes2 = /* @__PURE__ */ new Set()) {
  if (!allRoutes2.has(route)) {
    allRoutes2.add(route);
    if (route.children && !route.index) {
      route.children.forEach((child) => {
        const childRoutes = getChildRoutesRecursively(child, allRoutes2);
        childRoutes.forEach((r) => {
          allRoutes2.add(r);
        });
      });
    }
  }
  return allRoutes2;
}
function pickPath(match) {
  return trimWildcard(match.route.path || "");
}
function pickSplat(match) {
  return match.params["*"] || "";
}
function trimWildcard(path) {
  return path[path.length - 1] === "*" ? path.slice(0, -1) : path;
}
function trimSlash(path) {
  return path[path.length - 1] === "/" ? path.slice(0, -1) : path;
}
function prefixWithSlash(path) {
  return path[0] === "/" ? path : `/${path}`;
}
function rebuildRoutePathFromAllRoutes(allRoutes2, location) {
  const matchedRoutes = _matchRoutes(allRoutes2, location);
  if (!matchedRoutes || matchedRoutes.length === 0) {
    return "";
  }
  for (const match of matchedRoutes) {
    if (match.route.path && match.route.path !== "*") {
      const path = pickPath(match);
      const strippedPath = stripBasenameFromPathname(location.pathname, prefixWithSlash(match.pathnameBase));
      if (location.pathname === strippedPath) {
        return trimSlash(strippedPath);
      }
      return trimSlash(
        trimSlash(path || "") + prefixWithSlash(
          rebuildRoutePathFromAllRoutes(
            allRoutes2.filter((route) => route !== match.route),
            {
              pathname: strippedPath
            }
          )
        )
      );
    }
  }
  return "";
}
function getNormalizedName(routes, location, branches, basename = "") {
  if (!routes || routes.length === 0) {
    return [_stripBasename ? stripBasenameFromPathname(location.pathname, basename) : location.pathname, "url"];
  }
  let pathBuilder = "";
  if (branches) {
    for (const branch of branches) {
      const route = branch.route;
      if (route) {
        if (route.index) {
          return sendIndexPath(pathBuilder, branch.pathname, basename);
        }
        const path = route.path;
        if (path && !pathIsWildcardAndHasChildren(path, branch)) {
          const newPath = path[0] === "/" || pathBuilder[pathBuilder.length - 1] === "/" ? path : `/${path}`;
          pathBuilder = trimSlash(pathBuilder) + prefixWithSlash(newPath);
          if (trimSlash(location.pathname) === trimSlash(basename + branch.pathname)) {
            if (
              // If the route defined on the element is something like
              // <Route path="/stores/:storeId/products/:productId" element={<div>Product</div>} />
              // We should check against the branch.pathname for the number of / separators
              getNumberOfUrlSegments(pathBuilder) !== getNumberOfUrlSegments(branch.pathname) && // We should not count wildcard operators in the url segments calculation
              !pathEndsWithWildcard(pathBuilder)
            ) {
              return [(_stripBasename ? "" : basename) + newPath, "route"];
            }
            if (pathIsWildcardAndHasChildren(pathBuilder, branch)) {
              pathBuilder = pathBuilder.slice(0, -1);
            }
            return [(_stripBasename ? "" : basename) + pathBuilder, "route"];
          }
        }
      }
    }
  }
  const fallbackTransactionName = _stripBasename ? stripBasenameFromPathname(location.pathname, basename) : location.pathname || "/";
  return [fallbackTransactionName, "url"];
}
function updatePageloadTransaction(activeRootSpan, location, routes, matches, basename, allRoutes2) {
  const branches = Array.isArray(matches) ? matches : _matchRoutes(allRoutes2 || routes, location, basename);
  if (branches) {
    let name, source = "url";
    const isInDescendantRoute = locationIsInsideDescendantRoute(location, allRoutes2 || routes);
    if (isInDescendantRoute) {
      name = prefixWithSlash(rebuildRoutePathFromAllRoutes(allRoutes2 || routes, location));
      source = "route";
    }
    if (!isInDescendantRoute || !name) {
      [name, source] = getNormalizedName(routes, location, branches, basename);
    }
    getCurrentScope().setTransactionName(name || "/");
    if (activeRootSpan) {
      activeRootSpan.updateName(name);
      activeRootSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, source);
    }
  }
}
function createV6CompatibleWithSentryReactRouterRouting(Routes, version3) {
  if (!_useEffect || !_useLocation || !_useNavigationType || !_createRoutesFromChildren || !_matchRoutes) {
    DEBUG_BUILD && logger.warn(`reactRouterV6Instrumentation was unable to wrap Routes because of one or more missing parameters.
      useEffect: ${_useEffect}. useLocation: ${_useLocation}. useNavigationType: ${_useNavigationType}.
      createRoutesFromChildren: ${_createRoutesFromChildren}. matchRoutes: ${_matchRoutes}.`);
    return Routes;
  }
  const SentryRoutes = (props) => {
    const isMountRenderPass = React4.useRef(true);
    const location = _useLocation();
    const navigationType = _useNavigationType();
    _useEffect(
      () => {
        const routes = _createRoutesFromChildren(props.children);
        if (isMountRenderPass.current) {
          addRoutesToAllRoutes(routes);
          updatePageloadTransaction(getActiveRootSpan2(), location, routes, void 0, void 0, Array.from(allRoutes));
          isMountRenderPass.current = false;
        } else {
          handleNavigation({
            location,
            routes,
            navigationType,
            version: version3,
            allRoutes: Array.from(allRoutes)
          });
        }
      },
      // `props.children` is purposely not included in the dependency array, because we do not want to re-run this effect
      // when the children change. We only want to start transactions when the location or navigation type change.
      [location, navigationType]
    );
    return React4.createElement(Routes, { ...props });
  };
  hoistNonReactStatics(SentryRoutes, Routes);
  return SentryRoutes;
}
function getActiveRootSpan2() {
  const span = getActiveSpan();
  const rootSpan = span ? getRootSpan(span) : void 0;
  if (!rootSpan) {
    return void 0;
  }
  const op = spanToJSON(rootSpan).op;
  return op === "navigation" || op === "pageload" ? rootSpan : void 0;
}
function getNumberOfUrlSegments(url) {
  return url.split(/\\?\//).filter((s) => s.length > 0 && s !== ",").length;
}

// ../node_modules/@sentry/react/build/esm/reactrouterv6.js
function reactRouterV6BrowserTracingIntegration(options) {
  return createReactRouterV6CompatibleTracingIntegration(options, "6");
}
function wrapUseRoutesV6(origUseRoutes) {
  return createV6CompatibleWrapUseRoutes(origUseRoutes, "6");
}
function wrapCreateBrowserRouterV6(createRouterFunction) {
  return createV6CompatibleWrapCreateBrowserRouter(createRouterFunction, "6");
}
function wrapCreateMemoryRouterV6(createMemoryRouterFunction) {
  return createV6CompatibleWrapCreateMemoryRouter(createMemoryRouterFunction, "6");
}
function withSentryReactRouterV6Routing(routes) {
  return createV6CompatibleWithSentryReactRouterRouting(routes, "6");
}

// ../node_modules/@sentry/react/build/esm/reactrouterv7.js
function reactRouterV7BrowserTracingIntegration(options) {
  return createReactRouterV6CompatibleTracingIntegration(options, "7");
}
function withSentryReactRouterV7Routing(routes) {
  return createV6CompatibleWithSentryReactRouterRouting(routes, "7");
}
function wrapCreateBrowserRouterV7(createRouterFunction) {
  return createV6CompatibleWrapCreateBrowserRouter(createRouterFunction, "7");
}
function wrapCreateMemoryRouterV7(createMemoryRouterFunction) {
  return createV6CompatibleWrapCreateMemoryRouter(createMemoryRouterFunction, "7");
}
function wrapUseRoutesV7(origUseRoutes) {
  return createV6CompatibleWrapUseRoutes(origUseRoutes, "7");
}
export {
  BrowserClient,
  ErrorBoundary,
  OpenFeatureIntegrationHook,
  Profiler,
  SDK_VERSION,
  SEMANTIC_ATTRIBUTE_SENTRY_OP,
  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,
  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,
  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,
  Scope,
  WINDOW,
  addBreadcrumb,
  addEventProcessor,
  addIntegration,
  breadcrumbsIntegration,
  browserApiErrorsIntegration,
  browserProfilingIntegration,
  browserSessionIntegration,
  browserTracingIntegration,
  buildLaunchDarklyFlagUsedHandler,
  captureConsoleIntegration,
  captureEvent,
  captureException,
  captureFeedback,
  captureMessage,
  captureReactException,
  captureSession,
  chromeStackLineParser,
  close,
  consoleLoggingIntegration,
  contextLinesIntegration,
  continueTrace,
  createReduxEnhancer,
  createTransport,
  createUserFeedbackEnvelope,
  dedupeIntegration,
  defaultRequestInstrumentationOptions,
  defaultStackLineParsers,
  defaultStackParser,
  diagnoseSdkConnectivity,
  endSession,
  eventFiltersIntegration,
  eventFromException,
  eventFromMessage,
  exceptionFromError,
  extraErrorDataIntegration,
  featureFlagsIntegration,
  feedbackAsyncIntegration,
  feedbackSyncIntegration as feedbackIntegration,
  feedbackSyncIntegration,
  flush,
  forceLoad,
  functionToStringIntegration,
  geckoStackLineParser,
  getActiveSpan,
  getClient,
  getCurrentScope,
  getDefaultIntegrations,
  getFeedback,
  getGlobalScope,
  getIsolationScope,
  getReplay,
  getRootSpan,
  getSpanDescendants,
  getSpanStatusFromHttpCode,
  getTraceData,
  globalHandlersIntegration,
  graphqlClientIntegration,
  httpClientIntegration,
  httpContextIntegration,
  inboundFiltersIntegration,
  init2 as init,
  instrumentOutgoingRequests,
  instrumentSupabaseClient,
  isEnabled,
  isInitialized,
  lastEventId,
  launchDarklyIntegration,
  lazyLoadIntegration,
  linkedErrorsIntegration,
  log_exports as logger,
  makeBrowserOfflineTransport,
  makeFetchTransport,
  makeMultiplexedTransport,
  moduleMetadataIntegration,
  onLoad,
  openFeatureIntegration,
  opera10StackLineParser,
  opera11StackLineParser,
  parameterize,
  reactErrorHandler,
  reactRouterV3BrowserTracingIntegration,
  reactRouterV4BrowserTracingIntegration,
  reactRouterV5BrowserTracingIntegration,
  reactRouterV6BrowserTracingIntegration,
  reactRouterV7BrowserTracingIntegration,
  registerSpanErrorInstrumentation,
  replayCanvasIntegration,
  replayIntegration,
  reportingObserverIntegration,
  rewriteFramesIntegration,
  sendFeedback,
  setContext,
  setCurrentClient,
  setExtra,
  setExtras,
  setHttpStatus,
  setMeasurement,
  setTag,
  setTags,
  setUser,
  showReportDialog,
  spanToBaggageHeader,
  spanToJSON,
  spanToTraceHeader,
  spotlightBrowserIntegration,
  startBrowserTracingNavigationSpan,
  startBrowserTracingPageLoadSpan,
  startInactiveSpan,
  startNewTrace,
  startSession,
  startSpan,
  startSpanManual,
  statsigIntegration,
  supabaseIntegration,
  suppressTracing,
  tanstackRouterBrowserTracingIntegration,
  thirdPartyErrorFilterIntegration,
  unleashIntegration,
  updateSpanName,
  useProfiler,
  winjsStackLineParser,
  withActiveSpan,
  withErrorBoundary,
  withIsolationScope,
  withProfiler,
  withScope,
  withSentryReactRouterV6Routing,
  withSentryReactRouterV7Routing,
  withSentryRouting,
  wrapCreateBrowserRouterV6,
  wrapCreateBrowserRouterV7,
  wrapCreateMemoryRouterV6,
  wrapCreateMemoryRouterV7,
  wrapUseRoutesV6,
  wrapUseRoutesV7,
  zodErrorsIntegration
};
/*! Bundled license information:

react-is/cjs/react-is.development.js:
  (** @license React v16.13.1
   * react-is.development.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=@sentry_react.js.map
