{"version": 3, "sources": ["../../../../node_modules/@unpic/react/dist/chunk-VTEFGNYT.mjs", "../../../../node_modules/@unpic/react/dist/chunk-SNIEDJZS.mjs", "../../../../node_modules/@unpic/core/dist/chunk-7DG3H6KO.mjs", "../../../../node_modules/unpic/esm/data/domains.js", "../../../../node_modules/unpic/esm/data/subdomains.js", "../../../../node_modules/unpic/esm/data/paths.js", "../../../../node_modules/unpic/esm/src/utils.js", "../../../../node_modules/unpic/esm/src/detect.js", "../../../../node_modules/unpic/esm/src/providers/appwrite.js", "../../../../node_modules/unpic/esm/src/providers/astro.js", "../../../../node_modules/unpic/esm/src/providers/builder.io.js", "../../../../node_modules/unpic/esm/src/providers/bunny.js", "../../../../node_modules/unpic/esm/src/providers/cloudflare.js", "../../../../node_modules/unpic/esm/src/providers/cloudflare_images.js", "../../../../node_modules/unpic/esm/src/providers/cloudimage.js", "../../../../node_modules/unpic/esm/src/providers/cloudinary.js", "../../../../node_modules/unpic/esm/src/providers/contentful.js", "../../../../node_modules/unpic/esm/src/providers/contentstack.js", "../../../../node_modules/unpic/esm/src/providers/directus.js", "../../../../node_modules/unpic/esm/src/providers/hygraph.js", "../../../../node_modules/unpic/esm/src/providers/imageengine.js", "../../../../node_modules/unpic/esm/src/providers/imagekit.js", "../../../../node_modules/unpic/esm/src/providers/imgix.js", "../../../../node_modules/unpic/esm/src/providers/ipx.js", "../../../../node_modules/unpic/esm/src/providers/keycdn.js", "../../../../node_modules/unpic/esm/src/providers/kontent.ai.js", "../../../../node_modules/unpic/esm/src/providers/netlify.js", "../../../../node_modules/unpic/esm/src/providers/vercel.js", "../../../../node_modules/unpic/esm/src/providers/nextjs.js", "../../../../node_modules/unpic/esm/src/providers/scene7.js", "../../../../node_modules/unpic/esm/src/providers/shopify.js", "../../../../node_modules/unpic/esm/src/providers/storyblok.js", "../../../../node_modules/unpic/esm/src/providers/supabase.js", "../../../../node_modules/unpic/esm/src/providers/uploadcare.js", "../../../../node_modules/unpic/esm/src/providers/wordpress.js", "../../../../node_modules/unpic/esm/src/transform.js", "../../../../node_modules/@unpic/core/dist/auto.mjs"], "sourcesContent": ["// src/camelize.ts\nimport * as React from \"react\";\nvar nestedKeys = /* @__PURE__ */ new Set([\"style\"]);\nvar isNewReact = \"use\" in React;\nvar fixedMap = {\n  srcset: \"srcSet\",\n  fetchpriority: isNewReact ? \"fetchPriority\" : \"fetchpriority\"\n};\nvar camelize = (key) => {\n  if (key.startsWith(\"data-\") || key.startsWith(\"aria-\")) {\n    return key;\n  }\n  return fixedMap[key] || key.replace(/-./g, (suffix) => suffix[1].toUpperCase());\n};\nfunction camelizeProps(props) {\n  return Object.fromEntries(\n    Object.entries(props).map(([k, v]) => [\n      camelize(k),\n      nestedKeys.has(k) && v && typeof v !== \"string\" ? camelizeProps(v) : v\n    ])\n  );\n}\n\nexport {\n  camelizeProps\n};\n", "import {\n  camelizeProps\n} from \"./chunk-VTEFGNYT.mjs\";\n\n// src/image.tsx\nimport * as React from \"react\";\nimport { transformProps } from \"@unpic/core\";\nimport { jsx } from \"react/jsx-runtime\";\nvar Image = React.forwardRef(\n  function Image2(props, ref) {\n    const camelizedProps = camelizeProps(\n      transformProps(props)\n    );\n    return /* @__PURE__ */ jsx(\"img\", { ...camelizedProps, ref });\n  }\n);\n\n// src/source.tsx\nimport * as React2 from \"react\";\nimport { transformSourceProps } from \"@unpic/core\";\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar Source = React2.forwardRef(\n  function Source2(props, ref) {\n    const camelizedProps = camelizeProps(\n      transformSourceProps(\n        props\n      )\n    );\n    return /* @__PURE__ */ jsx2(\"source\", { ...camelizedProps, ref });\n  }\n);\n\nexport {\n  Image,\n  Source\n};\n", "// src/base.ts\nvar getSizes = (width, layout) => {\n  if (!width || !layout) {\n    return void 0;\n  }\n  switch (layout) {\n    // If screen is wider than the max size, image width is the max size,\n    // otherwise it's the width of the screen\n    case `constrained`:\n      return `(min-width: ${width}px) ${width}px, 100vw`;\n    // Image is always the same width, whatever the size of the screen\n    case `fixed`:\n      return `${width}px`;\n    // Image is always the width of the screen\n    case `fullWidth`:\n      return `100vw`;\n    default:\n      return void 0;\n  }\n};\nvar pixelate = (value) => value || value === 0 ? `${value}px` : void 0;\nvar getStyle = ({\n  width,\n  height,\n  aspectRatio,\n  layout,\n  objectFit = \"cover\",\n  background\n}) => {\n  const styleEntries = [\n    [\"object-fit\", objectFit]\n  ];\n  if (background?.startsWith(\"https:\") || background?.startsWith(\"http:\") || background?.startsWith(\"data:\") || background?.startsWith(\"/\")) {\n    styleEntries.push([\"background-image\", `url(${background})`]);\n    styleEntries.push([\"background-size\", \"cover\"]);\n    styleEntries.push([\"background-repeat\", \"no-repeat\"]);\n  } else {\n    styleEntries.push([\"background\", background]);\n  }\n  if (layout === \"fixed\") {\n    styleEntries.push([\"width\", pixelate(width)]);\n    styleEntries.push([\"height\", pixelate(height)]);\n  }\n  if (layout === \"constrained\") {\n    styleEntries.push([\"max-width\", pixelate(width)]);\n    styleEntries.push([\"max-height\", pixelate(height)]);\n    styleEntries.push([\n      \"aspect-ratio\",\n      aspectRatio ? `${aspectRatio}` : void 0\n    ]);\n    styleEntries.push([\"width\", \"100%\"]);\n  }\n  if (layout === \"fullWidth\") {\n    styleEntries.push([\"width\", \"100%\"]);\n    styleEntries.push([\n      \"aspect-ratio\",\n      aspectRatio ? `${aspectRatio}` : void 0\n    ]);\n    styleEntries.push([\"height\", pixelate(height)]);\n  }\n  return Object.fromEntries(\n    styleEntries.filter(([, value]) => value)\n  );\n};\nvar DEFAULT_RESOLUTIONS = [\n  6016,\n  // 6K\n  5120,\n  // 5K\n  4480,\n  // 4.5K\n  3840,\n  // 4K\n  3200,\n  // QHD+\n  2560,\n  // WQXGA\n  2048,\n  // QXGA\n  1920,\n  // 1080p\n  1668,\n  // Various iPads\n  1280,\n  // 720p\n  1080,\n  // iPhone 6-8 Plus\n  960,\n  // older horizontal phones\n  828,\n  // iPhone XR/11\n  750,\n  // iPhone 6-8\n  640\n  // older and lower-end phones\n];\nvar LOW_RES_WIDTH = 24;\nvar getBreakpoints = ({\n  width,\n  layout,\n  resolutions = DEFAULT_RESOLUTIONS\n}) => {\n  if (layout === \"fullWidth\") {\n    return resolutions;\n  }\n  if (!width) {\n    return [];\n  }\n  const doubleWidth = width * 2;\n  if (layout === \"fixed\") {\n    return [width, doubleWidth];\n  }\n  if (layout === \"constrained\") {\n    return [\n      // Always include the image at 1x and 2x the specified width\n      width,\n      doubleWidth,\n      // Filter out any resolutions that are larger than the double-res image\n      ...resolutions.filter((w) => w < doubleWidth)\n    ];\n  }\n  return [];\n};\nvar getSrcSetEntries = ({\n  src,\n  width,\n  layout = \"constrained\",\n  height,\n  aspectRatio,\n  breakpoints,\n  format\n}) => {\n  breakpoints ||= getBreakpoints({ width, layout });\n  return breakpoints.sort((a, b) => a - b).map((bp) => {\n    let transformedHeight;\n    if (height && aspectRatio) {\n      transformedHeight = Math.round(bp / aspectRatio);\n    }\n    return {\n      url: src,\n      width: bp,\n      height: transformedHeight,\n      format\n    };\n  });\n};\nvar getSrcSet = (options) => {\n  let { src, transformer, operations } = options;\n  if (!transformer) {\n    return \"\";\n  }\n  return getSrcSetEntries(options).map(({ url: _, ...transform }) => {\n    const url = transformer(\n      src,\n      { ...operations, ...transform },\n      options.options\n    );\n    return `${url?.toString()} ${transform.width}w`;\n  }).join(\",\\n\");\n};\nfunction transformSharedProps({\n  width,\n  height,\n  priority,\n  layout = \"constrained\",\n  aspectRatio,\n  ...props\n}) {\n  width = width && Number(width) || void 0;\n  height = height && Number(height) || void 0;\n  if (priority) {\n    props.loading ||= \"eager\";\n    props.fetchpriority ||= \"high\";\n  } else {\n    props.loading ||= \"lazy\";\n    props.decoding ||= \"async\";\n  }\n  if (props.alt === \"\") {\n    props.role ||= \"presentation\";\n  }\n  if (aspectRatio) {\n    if (width) {\n      if (height) {\n      } else {\n        height = Math.round(width / aspectRatio);\n      }\n    } else if (height) {\n      width = Math.round(height * aspectRatio);\n    } else if (layout !== \"fullWidth\") {\n    }\n  } else if (width && height) {\n    aspectRatio = width / height;\n  } else if (layout !== \"fullWidth\") {\n  }\n  return {\n    width,\n    height,\n    aspectRatio,\n    layout,\n    ...props\n  };\n}\nfunction transformBaseImageProps(props) {\n  let {\n    src,\n    transformer,\n    background,\n    layout,\n    objectFit,\n    breakpoints,\n    width,\n    height,\n    aspectRatio,\n    unstyled,\n    operations,\n    options,\n    ...transformedProps\n  } = transformSharedProps(props);\n  if (transformer && background === \"auto\") {\n    const lowResHeight = aspectRatio ? Math.round(LOW_RES_WIDTH / aspectRatio) : void 0;\n    const lowResImage = transformer(\n      src,\n      {\n        width: LOW_RES_WIDTH,\n        height: lowResHeight\n      },\n      options\n    );\n    if (lowResImage) {\n      background = lowResImage.toString();\n    }\n  }\n  const styleProps = {\n    width,\n    height,\n    aspectRatio,\n    layout,\n    objectFit,\n    background\n  };\n  transformedProps.sizes ||= getSizes(width, layout);\n  if (!unstyled) {\n    transformedProps.style = {\n      ...getStyle(styleProps),\n      ...transformedProps.style\n    };\n  }\n  if (transformer) {\n    transformedProps.srcset = getSrcSet({\n      src,\n      width,\n      height,\n      aspectRatio,\n      layout,\n      breakpoints,\n      transformer,\n      operations,\n      options\n    });\n    const transformed = transformer(\n      src,\n      { ...operations, width, height },\n      options\n    );\n    if (transformed) {\n      src = transformed;\n    }\n    if (layout === \"fullWidth\" || layout === \"constrained\") {\n      width = void 0;\n      height = void 0;\n    }\n  }\n  return {\n    ...transformedProps,\n    src: src?.toString(),\n    width,\n    height\n  };\n}\nfunction normalizeImageType(type) {\n  if (!type) {\n    return {};\n  }\n  if (type.startsWith(\"image/\")) {\n    return {\n      format: type.slice(6),\n      mimeType: type\n    };\n  }\n  return {\n    format: type,\n    mimeType: `image/${type === \"jpg\" ? \"jpeg\" : type}`\n  };\n}\nfunction transformBaseSourceProps({\n  media,\n  type,\n  ...props\n}) {\n  let {\n    src,\n    transformer,\n    layout,\n    breakpoints,\n    width,\n    height,\n    aspectRatio,\n    sizes,\n    loading,\n    decoding,\n    operations,\n    options,\n    ...rest\n  } = transformSharedProps(props);\n  if (!transformer) {\n    return {};\n  }\n  const { format, mimeType } = normalizeImageType(type);\n  sizes ||= getSizes(width, layout);\n  const srcset = getSrcSet({\n    src,\n    width,\n    height,\n    aspectRatio,\n    layout,\n    breakpoints,\n    transformer,\n    format,\n    operations,\n    options\n  });\n  const transformed = transformer(\n    src,\n    { ...operations, width, height },\n    options\n  );\n  if (transformed) {\n    src = transformed;\n  }\n  const returnObject = {\n    ...rest,\n    sizes,\n    srcset\n  };\n  if (media) {\n    returnObject.media = media;\n  }\n  if (mimeType) {\n    returnObject.type = mimeType;\n  }\n  return returnObject;\n}\nfunction inferImageDimensions(props, imageData) {\n  const aspectRatio = props.aspectRatio || imageData.width / imageData.height;\n  let { width, height } = props;\n  if (!width) {\n    if (height) {\n      width = height * aspectRatio;\n    } else {\n      width = imageData.width;\n    }\n  }\n  if (!height) {\n    if (width) {\n      height = width / aspectRatio;\n    } else {\n      height = imageData.height;\n    }\n  }\n  return { width, height };\n}\n\nexport {\n  getSizes,\n  getStyle,\n  DEFAULT_RESOLUTIONS,\n  getBreakpoints,\n  getSrcSetEntries,\n  getSrcSet,\n  transformSharedProps,\n  transformBaseImageProps,\n  normalizeImageType,\n  transformBaseSourceProps,\n  inferImageDimensions\n};\n", "export default {\n    \"images.ctfassets.net\": \"contentful\",\n    \"cdn.builder.io\": \"builder.io\",\n    \"images.prismic.io\": \"imgix\",\n    \"www.datocms-assets.com\": \"imgix\",\n    \"cdn.sanity.io\": \"imgix\",\n    \"images.unsplash.com\": \"imgix\",\n    \"cdn.shopify.com\": \"shopify\",\n    \"s7d1.scene7.com\": \"scene7\",\n    \"ip.keycdn.com\": \"keycdn\",\n    \"assets.caisy.io\": \"bunny\",\n    \"images.contentstack.io\": \"contentstack\",\n    \"ucarecdn.com\": \"uploadcare\",\n    \"imagedelivery.net\": \"cloudflare_images\"\n};\n", "export default {\n    \"imgix.net\": \"imgix\",\n    \"wp.com\": \"wordpress\",\n    \"files.wordpress.com\": \"wordpress\",\n    \"b-cdn.net\": \"bunny\",\n    \"storyblok.com\": \"storyblok\",\n    \"kc-usercontent.com\": \"kontent.ai\",\n    \"cloudinary.com\": \"cloudinary\",\n    \"kxcdn.com\": \"keycdn\",\n    \"imgeng.in\": \"imageengine\",\n    \"imagekit.io\": \"imagekit\",\n    \"cloudimg.io\": \"cloudimage\",\n    \"ucarecdn.com\": \"uploadcare\",\n    \"supabase.co\": \"supabase\",\n    \"graphassets.com\": \"hygraph\"\n};\n", "export default {\n    \"/cdn-cgi/image/\": \"cloudflare\",\n    \"/cdn-cgi/imagedelivery/\": \"cloudflare_images\",\n    \"/_next/image\": \"nextjs\",\n    \"/_vercel/image\": \"vercel\",\n    \"/is/image\": \"scene7\",\n    \"/_ipx/\": \"ipx\",\n    \"/_image\": \"astro\",\n    \"/.netlify/images\": \"netlify\",\n    \"/storage/v1/object/public/\": \"supabase\",\n    \"/storage/v1/render/image/public/\": \"supabase\",\n    \"/v1/storage/buckets/\": \"appwrite\"\n};\n", "export function roundIfNumeric(value) {\n    if (!value) {\n        // deno-lint-ignore no-explicit-any\n        return value;\n    }\n    const num = Number(value);\n    if (isNaN(num)) {\n        // deno-lint-ignore no-explicit-any\n        return value;\n    }\n    // deno-lint-ignore no-explicit-any\n    return Math.round(num);\n}\n/**\n * Given a URL object, returns path and query params\n */\nexport const toRelativeUrl = (url) => {\n    const { pathname, search } = url;\n    return `${pathname}${search}`;\n};\n/**\n * Returns a URL string that may be relative or absolute\n */\nexport const toCanonicalUrlString = (url) => {\n    return url.hostname === \"n\" ? toRelativeUrl(url) : url.toString();\n};\n/**\n * Normalises a URL object or string URL to a URL object.\n */\nexport const toUrl = (url, base) => {\n    return typeof url === \"string\" ? new URL(url, base ?? \"http://n/\") : url;\n};\n/**\n * Escapes a string, even if it's URL-safe\n */\nexport const escapeChar = (text) => text === \" \" ? \"+\" : (\"%\" +\n    text.charCodeAt(0).toString(16).toUpperCase().padStart(2, \"0\"));\nexport const stripLeadingSlash = (str) => str?.startsWith(\"/\") ? str.slice(1) : str;\nexport const stripTrailingSlash = (str) => str?.endsWith(\"/\") ? str.slice(0, -1) : str;\nexport const addLeadingSlash = (str) => str?.startsWith(\"/\") ? str : `/${str}`;\nexport const addTrailingSlash = (str) => str?.endsWith(\"/\") ? str : `${str}/`;\n/**\n * Creates a formatter given an operation joiner and key/value joiner\n */\nexport const createFormatter = (kvSeparator, paramSeparator) => {\n    const encodedValueJoiner = escapeChar(kvSeparator);\n    const encodedOperationJoiner = escapeChar(paramSeparator);\n    function escape(value) {\n        return encodeURIComponent(value).replaceAll(kvSeparator, encodedValueJoiner)\n            .replaceAll(paramSeparator, encodedOperationJoiner);\n    }\n    function format(key, value) {\n        return `${escape(key)}${kvSeparator}${escape(String(value))}`;\n    }\n    return (operations) => {\n        const ops = Array.isArray(operations)\n            ? operations\n            : Object.entries(operations);\n        return ops.flatMap(([key, value]) => {\n            if (value === undefined || value === null) {\n                return [];\n            }\n            if (Array.isArray(value)) {\n                return value.map((v) => format(key, v));\n            }\n            return format(key, value);\n        }).join(paramSeparator);\n    };\n};\n/**\n * Creates a parser given an operation joiner and key/value joiner\n */\nexport const createParser = (kvSeparator, paramSeparator) => {\n    if (kvSeparator === \"=\" && paramSeparator === \"&\") {\n        return queryParser;\n    }\n    return (url) => {\n        const urlString = url.toString();\n        return Object.fromEntries(urlString.split(paramSeparator).map((pair) => {\n            const [key, value] = pair.split(kvSeparator);\n            return [decodeURI(key), decodeURI(value)];\n        }));\n    };\n};\n/**\n * Clamp width and height, maintaining aspect ratio\n */\nexport function clampDimensions(operations, maxWidth = 4000, maxHeight = 4000) {\n    let { width, height } = operations;\n    width = Number(width) || undefined;\n    height = Number(height) || undefined;\n    if (width && width > maxWidth) {\n        if (height) {\n            height = Math.round(height * maxWidth / width);\n        }\n        width = maxWidth;\n    }\n    if (height && height > maxHeight) {\n        if (width) {\n            width = Math.round(width * maxHeight / height);\n        }\n        height = maxHeight;\n    }\n    return { width, height };\n}\nexport function extractFromURL(url) {\n    const parsedUrl = toUrl(url);\n    const operations = Object.fromEntries(parsedUrl.searchParams.entries());\n    for (const key in [\"width\", \"height\", \"quality\"]) {\n        const value = operations[key];\n        if (value) {\n            const newVal = Number(value);\n            if (!isNaN(newVal)) {\n                // deno-lint-ignore no-explicit-any\n                operations[key] = newVal;\n            }\n        }\n    }\n    parsedUrl.search = \"\";\n    return {\n        operations: operations,\n        src: toCanonicalUrlString(parsedUrl),\n    };\n}\nexport function normaliseOperations({ keyMap = {}, formatMap = {}, defaults = {} }, operations) {\n    if (operations.format && operations.format in formatMap) {\n        operations.format = formatMap[operations.format];\n    }\n    if (operations.width) {\n        operations.width = roundIfNumeric(operations.width);\n    }\n    if (operations.height) {\n        operations.height = roundIfNumeric(operations.height);\n    }\n    for (const k in keyMap) {\n        if (!Object.prototype.hasOwnProperty.call(keyMap, k)) {\n            continue;\n        }\n        const key = k;\n        if (keyMap[key] === false) {\n            delete operations[key];\n            continue;\n        }\n        if (keyMap[key] && operations[key]) {\n            operations[keyMap[key]] = operations[key];\n            delete operations[key];\n        }\n    }\n    for (const k in defaults) {\n        if (!Object.prototype.hasOwnProperty.call(defaults, k)) {\n            continue;\n        }\n        const key = k;\n        const value = defaults[key];\n        if (!operations[key] && value !== undefined) {\n            if (keyMap[key] === false) {\n                continue;\n            }\n            const resolvedKey = keyMap[key] ?? key;\n            if (resolvedKey in operations) {\n                continue;\n            }\n            // deno-lint-ignore no-explicit-any\n            operations[resolvedKey] = value;\n        }\n    }\n    return operations;\n}\nconst invertMap = (\n// deno-lint-ignore no-explicit-any\nmap) => Object.fromEntries(Object.entries(map).map(([k, v]) => [v, k]));\nexport function denormaliseOperations({ keyMap = {}, formatMap = {}, defaults = {} }, operations) {\n    const invertedKeyMap = invertMap(keyMap);\n    const invertedFormatMap = invertMap(formatMap);\n    const ops = normaliseOperations({\n        keyMap: invertedKeyMap,\n        formatMap: invertedFormatMap,\n        defaults,\n    }, operations);\n    if (ops.width) {\n        ops.width = roundIfNumeric(ops.width);\n    }\n    if (ops.height) {\n        ops.height = roundIfNumeric(ops.height);\n    }\n    const q = Number(ops.quality);\n    if (!isNaN(q)) {\n        ops.quality = q;\n    }\n    return ops;\n}\n// Parses a query string\nconst queryParser = (url) => {\n    const parsedUrl = toUrl(url);\n    return Object.fromEntries(parsedUrl.searchParams.entries());\n};\nexport function createOperationsGenerator({ kvSeparator = \"=\", paramSeparator = \"&\", ...options } = {}) {\n    const formatter = createFormatter(kvSeparator, paramSeparator);\n    return (operations) => {\n        const normalisedOperations = normaliseOperations(options, operations);\n        return formatter(normalisedOperations);\n    };\n}\nexport function createOperationsParser({ kvSeparator = \"=\", paramSeparator = \"&\", defaults: _, ...options } = {}) {\n    const parser = createParser(kvSeparator, paramSeparator);\n    return (url) => {\n        const operations = url ? parser(url) : {};\n        return denormaliseOperations(options, operations);\n    };\n}\nexport function createOperationsHandlers(config) {\n    const operationsGenerator = createOperationsGenerator(config);\n    const operationsParser = createOperationsParser(config);\n    return { operationsGenerator, operationsParser };\n}\nexport function paramToBoolean(value) {\n    if (value === undefined || value === null) {\n        return undefined;\n    }\n    try {\n        return Boolean(JSON.parse(value?.toString()));\n    }\n    catch {\n        return Boolean(value);\n    }\n}\nconst removeUndefined = (obj) => Object.fromEntries(Object.entries(obj).filter(([, value]) => value !== undefined));\nexport function createExtractAndGenerate(extract, generate) {\n    return ((src, operations, options) => {\n        const base = extract(src, options);\n        if (!base) {\n            return generate(src, operations, options);\n        }\n        return generate(base.src, {\n            ...base.operations,\n            ...removeUndefined(operations),\n        }, {\n            // deno-lint-ignore no-explicit-any\n            ...base.options,\n            ...options,\n        });\n    });\n}\n", "import domains from \"../data/domains.js\";\nimport subdomains from \"../data/subdomains.js\";\nimport paths from \"../data/paths.js\";\nimport { toUrl } from \"./utils.js\";\nconst cdnDomains = new Map(Object.entries(domains));\nconst cdnSubdomains = Object.entries(subdomains);\nconst cdnPaths = Object.entries(paths);\n/**\n * Detects the image CDN provider for a given URL.\n */\nexport function getProviderForUrl(url) {\n    return getProviderForUrlByDomain(url) || getProviderForUrlByPath(url);\n}\n/**\n * @deprecated Use `getProviderForUrl` instead.\n */\nexport const getImageCdnForUrl = getProviderForUrl;\nexport function getProviderForUrlByDomain(url) {\n    if (typeof url === \"string\" && !url.startsWith(\"https://\")) {\n        return false;\n    }\n    const { hostname } = toUrl(url);\n    const cdn = cdnDomains.get(hostname);\n    if (cdn) {\n        return cdn;\n    }\n    return cdnSubdomains.find(([subdomain]) => hostname.endsWith(subdomain))?.[1] || false;\n}\n/**\n * @deprecated Use `getProviderForUrlByDomain` instead.\n */\nexport const getImageCdnForUrlByDomain = getProviderForUrlByDomain;\n/**\n * Gets the image CDN provider for a given URL by its path.\n */\nexport function getProviderForUrlByPath(url) {\n    // Allow relative URLs\n    const { pathname } = toUrl(url);\n    return cdnPaths.find(([path]) => pathname.startsWith(path))?.[1] || false;\n}\n/**\n * @deprecated Use `getProviderForUrlByPath` instead.\n */\nexport const getImageCdnForUrlByPath = getProviderForUrlByPath;\n", "import { getProviderForUrlByPath } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst VIEW_URL_SUFFIX = \"/view?\";\nconst PREVIEW_URL_SUFFIX = \"/preview?\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        format: \"output\",\n    },\n    kvSeparator: \"=\",\n    paramSeparator: \"&\",\n});\nexport const generate = (src, modifiers) => {\n    const url = toUrl(src.toString().replace(VIEW_URL_SUFFIX, PREVIEW_URL_SUFFIX));\n    const projectParam = url.searchParams.get(\"project\") ?? \"\";\n    const operations = operationsGenerator(modifiers);\n    url.search = operations;\n    url.searchParams.append(\"project\", projectParam);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    if (getProviderForUrlByPath(url) !== \"appwrite\") {\n        return null;\n    }\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    // deno-lint-ignore no-explicit-any\n    delete operations.project;\n    const projectParam = parsedUrl.searchParams.get(\"project\") ?? \"\";\n    parsedUrl.search = \"\";\n    parsedUrl.searchParams.append(\"project\", projectParam);\n    const sourceUrl = parsedUrl.href;\n    return {\n        src: sourceUrl,\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createOperationsHandlers, stripTrailingSlash, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst DEFAULT_ENDPOINT = \"/_image\";\nconst { operationsParser, operationsGenerator } = createOperationsHandlers({\n    keyMap: {\n        format: \"f\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n    defaults: {\n        fit: \"cover\",\n    },\n});\nexport const generate = (src, modifiers, options) => {\n    const url = toUrl(`${stripTrailingSlash(options?.baseUrl ?? \"\")}${options?.endpoint ?? DEFAULT_ENDPOINT}`);\n    const operations = operationsGenerator(modifiers);\n    url.search = operations;\n    url.searchParams.set(\"href\", src.toString());\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const src = parsedUrl.searchParams.get(\"href\");\n    if (!src) {\n        return null;\n    }\n    parsedUrl.searchParams.delete(\"href\");\n    const operations = operationsParser(parsedUrl);\n    return {\n        src,\n        operations,\n        options: { baseUrl: parsedUrl.origin },\n    };\n};\nexport const transform = (src, operations, options = {}) => {\n    const url = toUrl(src);\n    if (url.pathname !== (options?.endpoint ?? DEFAULT_ENDPOINT)) {\n        return generate(src, operations, options);\n    }\n    const base = extract(src);\n    if (!base) {\n        return generate(src, operations, options);\n    }\n    options.baseUrl ??= base.options.baseUrl;\n    return generate(base.src, {\n        ...base.operations,\n        ...operations,\n    }, options);\n};\n", "import { createExtractAndGenerate, createOperationsGenerator, extractFromURL, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst operationsGenerator = createOperationsGenerator({\n    defaults: {\n        fit: \"cover\",\n        format: \"webp\",\n        sharp: true,\n    },\n});\nexport const extract = extractFromURL;\nexport const generate = (src, modifiers) => {\n    const operations = operationsGenerator(modifiers);\n    const url = toUrl(src);\n    url.search = operations;\n    return toCanonicalUrlString(url);\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsGenerator, extractFromURL, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst operationsGenerator = createOperationsGenerator({\n    keyMap: {\n        format: \"output\",\n    },\n});\nexport const extract = extractFromURL;\nexport const generate = (src, modifiers) => {\n    const operations = operationsGenerator(modifiers);\n    const url = toUrl(src);\n    url.search = operations;\n    return toCanonicalUrlString(url);\n};\nconst extractAndGenerate = createExtractAndGenerate(extract, generate);\nexport const transform = (src, operations) => {\n    const { width, height } = operations;\n    if (width && height) {\n        operations.aspect_ratio ??= `${Math.round(Number(width))}:${Math.round(Number(height))}`;\n    }\n    return extractAndGenerate(src, operations);\n};\n", "import { getProviderForUrlByPath } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, stripLeadingSlash, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        \"format\": \"f\",\n    },\n    defaults: {\n        format: \"auto\",\n        fit: \"cover\",\n    },\n    formatMap: {\n        jpg: \"jpeg\",\n    },\n    kvSeparator: \"=\",\n    paramSeparator: \",\",\n});\nexport const generate = (src, operations, options) => {\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(options?.domain ? `https://${options.domain}` : \"/\");\n    url.pathname = `/cdn-cgi/image/${modifiers}/${stripLeadingSlash(src.toString())}`;\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url, options) => {\n    if (getProviderForUrlByPath(url) !== \"cloudflare\") {\n        return null;\n    }\n    const parsedUrl = toUrl(url);\n    const [, , , modifiers, ...src] = parsedUrl.pathname.split(\"/\");\n    const operations = operationsParser(modifiers);\n    return {\n        src: toCanonicalUrlString(toUrl(src.join(\"/\"))),\n        operations,\n        options: {\n            domain: options?.domain ??\n                (parsedUrl.hostname === \"n\" ? undefined : parsedUrl.hostname),\n        },\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst cloudflareImagesRegex = /https?:\\/\\/(?<host>[^\\/]+)\\/cdn-cgi\\/imagedelivery\\/(?<accountHash>[^\\/]+)\\/(?<imageId>[^\\/]+)\\/*(?<transformations>[^\\/]+)*$/g;\nconst imagedeliveryRegex = /https?:\\/\\/(?<host>imagedelivery.net)\\/(?<accountHash>[^\\/]+)\\/(?<imageId>[^\\/]+)\\/*(?<transformations>[^\\/]+)*$/g;\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n        format: \"f\",\n    },\n    defaults: {\n        fit: \"cover\",\n    },\n    kvSeparator: \"=\",\n    paramSeparator: \",\",\n});\nfunction formatUrl(options, transformations) {\n    const { host, accountHash, imageId } = options;\n    if (!host || !accountHash || !imageId) {\n        throw new Error(\"Missing required Cloudflare Images options\");\n    }\n    const pathSegments = [\n        \"https:/\",\n        ...(host === \"imagedelivery.net\"\n            ? [host]\n            : [host, \"cdn-cgi\", \"imagedelivery\"]),\n        accountHash,\n        imageId,\n        transformations,\n    ].filter(Boolean);\n    return pathSegments.join(\"/\");\n}\nexport const generate = (_src, operations, options = {}) => {\n    const transformations = operationsGenerator(operations);\n    const url = formatUrl(options, transformations);\n    return toCanonicalUrlString(toUrl(url));\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const matches = [\n        ...parsedUrl.toString().matchAll(cloudflareImagesRegex),\n        ...parsedUrl.toString().matchAll(imagedeliveryRegex),\n    ];\n    if (!matches[0]?.groups) {\n        return null;\n    }\n    const { host, accountHash, imageId, transformations } = matches[0].groups;\n    const operations = operationsParser(transformations || \"\");\n    const options = { host, accountHash, imageId };\n    return {\n        src: formatUrl(options),\n        operations,\n        options: options,\n    };\n};\nexport const transform = (src, operations, options = {}) => {\n    const extracted = extract(src);\n    if (!extracted) {\n        throw new Error(\"Invalid Cloudflare Images URL\");\n    }\n    const newOperations = { ...extracted.operations, ...operations };\n    return generate(extracted.src, newOperations, {\n        ...extracted.options,\n        ...options,\n    });\n};\n", "import { getProviderForUrl } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        format: \"force_format\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n    defaults: {\n        org_if_sml: 1,\n    },\n});\nexport const generate = (src, modifiers = {}, { token } = {}) => {\n    if (!token) {\n        throw new Error(\"Token is required for Cloudimage URLs\" + src);\n    }\n    let srcString = src.toString();\n    srcString = srcString.replace(/^https?:\\/\\//, \"\");\n    if (srcString.includes(\"?\")) {\n        modifiers.ci_url_encoded = 1;\n        srcString = encodeURIComponent(srcString);\n    }\n    const operations = operationsGenerator(modifiers);\n    const url = new URL(`https://${token}.cloudimg.io/`);\n    url.pathname = srcString;\n    url.search = operations;\n    return url.toString();\n};\nexport const extract = (src, options = {}) => {\n    const url = toUrl(src);\n    if (getProviderForUrl(url) !== \"cloudimage\") {\n        return null;\n    }\n    const operations = operationsParser(url);\n    let originalSrc = url.pathname;\n    if (operations.ci_url_encoded) {\n        originalSrc = decodeURIComponent(originalSrc);\n        delete operations.ci_url_encoded;\n    }\n    options.token ??= url.hostname.replace(\".cloudimg.io\", \"\");\n    return {\n        src: `${url.protocol}/${originalSrc}`,\n        operations,\n        options,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createOperationsHandlers } from \"../utils.js\";\nconst publicRegex = /https?:\\/\\/(?<host>res\\.cloudinary\\.com)\\/(?<cloudName>[a-zA-Z0-9-]+)\\/(?<assetType>image|video|raw)\\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)\\/?(?<signature>s\\-\\-[a-zA-Z0-9]+\\-\\-)?\\/?(?<transformations>(?:[^_\\/]+_[^,\\/]+,?)*)?\\/(?:(?<version>v\\d+)\\/)?(?<id>(?:[^\\s\\/]+\\/)*[^\\s\\/]+(?:\\.[a-zA-Z0-9]+)?)$/;\nconst privateRegex = /https?:\\/\\/(?<host>(?<cloudName>[a-zA-Z0-9-]+)-res\\.cloudinary\\.com|[a-zA-Z0-9.-]+)\\/(?<assetType>image|video|raw)\\/(?<deliveryType>upload|fetch|private|authenticated|sprite|facebook|twitter|youtube|vimeo)\\/?(?<signature>s\\-\\-[a-zA-Z0-9]+\\-\\-)?\\/?(?<transformations>(?:[^_\\/]+_[^,\\/]+,?)*)?\\/(?:(?<version>v\\d+)\\/)?(?<id>(?:[^\\s\\/]+\\/)*[^\\s\\/]+(?:\\.[a-zA-Z0-9]+)?)$/;\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n        format: \"f\",\n        quality: \"q\",\n    },\n    defaults: {\n        format: \"auto\",\n        c: \"lfill\",\n    },\n    kvSeparator: \"_\",\n    paramSeparator: \",\",\n});\nexport const generate = (src, operations) => {\n    const group = parseCloudinaryUrl(src.toString());\n    if (!group) {\n        return src.toString();\n    }\n    group.transformations = operationsGenerator(operations);\n    return formatCloudinaryUrl(group);\n};\nfunction formatCloudinaryUrl({ host, cloudName, assetType, deliveryType, signature, transformations, version, id, }) {\n    const isPublic = host === \"res.cloudinary.com\";\n    return [\n        \"https:/\",\n        host,\n        isPublic ? cloudName : undefined,\n        assetType,\n        deliveryType,\n        signature,\n        transformations,\n        version,\n        id,\n    ].filter(Boolean).join(\"/\");\n}\nfunction parseCloudinaryUrl(url) {\n    let matches = url.toString().match(publicRegex);\n    if (!matches?.length) {\n        matches = url.toString().match(privateRegex);\n    }\n    if (!matches?.length) {\n        return null;\n    }\n    return matches.groups || {};\n}\nexport const extract = (url) => {\n    const group = parseCloudinaryUrl(url.toString());\n    if (!group) {\n        return null;\n    }\n    const { transformations: transformString = \"\", ...params } = group;\n    const src = formatCloudinaryUrl(params);\n    const operations = operationsParser(transformString) || {};\n    return {\n        src,\n        operations,\n        options: {\n            cloudName: params.cloudName,\n            domain: params.host,\n            privateCdn: params.host !== \"res.cloudinary.com\",\n        },\n    };\n};\nexport const transform = (src, operations) => {\n    const group = parseCloudinaryUrl(src.toString());\n    if (!group) {\n        return src.toString();\n    }\n    const existing = operationsParser(group.transformations || \"\");\n    group.transformations = operationsGenerator({\n        ...existing,\n        ...operations,\n    });\n    return formatCloudinaryUrl(group);\n};\n", "import { clampDimensions, createExtractAndGenerate, createOperationsGenerator, extractFromURL, toCanonicalUrlString, } from \"../utils.js\";\nconst operationsGenerator = createOperationsGenerator({\n    keyMap: {\n        format: \"fm\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n    defaults: {\n        fit: \"fill\",\n    },\n});\nexport const generate = (src, modifiers) => {\n    const operations = operationsGenerator(modifiers);\n    const url = new URL(src);\n    url.search = operations;\n    return toCanonicalUrlString(url);\n};\nexport const extract = extractFromURL;\nconst extractAndGenerate = createExtractAndGenerate(extract, generate);\nexport const transform = (src, operations) => {\n    const { width, height } = clampDimensions(operations, 4000, 4000);\n    return extractAndGenerate(src, {\n        ...operations,\n        width,\n        height,\n    });\n};\n", "import { createExtractAndGenerate, createOperationsGenerator, extractFromURL, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst operationsGenerator = createOperationsGenerator({\n    defaults: {\n        auto: \"webp\",\n        disable: \"upscale\",\n    },\n});\nexport const generate = (src, operations, { baseURL = \"https://images.contentstack.io/\" } = {}) => {\n    if (operations.width && operations.height) {\n        operations.fit ??= \"crop\";\n    }\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(src);\n    if (url.hostname === \"n\") {\n        url.protocol = \"https:\";\n        url.hostname = new URL(baseURL).hostname;\n    }\n    url.search = modifiers;\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const { src, operations } = extractFromURL(url) ?? {};\n    if (!operations || !src) {\n        return null;\n    }\n    const { origin } = toUrl(url);\n    return {\n        src,\n        operations: operations,\n        options: {\n            baseURL: origin,\n        },\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsGenerator, extractFromURL, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst operationsGenerator = createOperationsGenerator({\n    defaults: {\n        withoutEnlargement: true,\n        fit: \"cover\",\n    },\n});\nexport const generate = (src, operations) => {\n    if (Array.isArray(operations.transforms)) {\n        operations.transforms = JSON.stringify(operations.transforms);\n    }\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(src);\n    url.search = modifiers;\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const base = extractFromURL(url);\n    if (base?.operations?.transforms &&\n        typeof base.operations.transforms === \"string\") {\n        try {\n            base.operations.transforms = JSON.parse(base.operations.transforms);\n        }\n        catch {\n            return null;\n        }\n    }\n    return base;\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst hygraphRegex = /https:\\/\\/(?<region>[a-z0-9-]+)\\.graphassets\\.com\\/(?<envId>[a-zA-Z0-9]+)(?:\\/(?<transformations>.*?))?\\/(?<handle>[a-zA-Z0-9]+)$/;\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"width\",\n        height: \"height\",\n        format: \"format\",\n    },\n    defaults: {\n        format: \"auto\",\n        fit: \"crop\",\n    },\n});\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const matches = parsedUrl.toString().match(hygraphRegex);\n    if (!matches?.groups) {\n        return null;\n    }\n    const { region, envId, handle, transformations } = matches.groups;\n    // Parse any existing transformations from the URL\n    const operations = {};\n    if (transformations) {\n        const parts = transformations.split(\"/\");\n        parts.forEach((part) => {\n            const [operation, params] = part.split(\"=\");\n            if (operation === \"resize\" && params) {\n                params.split(\",\").forEach((param) => {\n                    const [key, value] = param.split(\":\");\n                    if (key === \"width\" || key === \"height\") {\n                        operations[key] = Number(value);\n                    }\n                    else if (key === \"fit\") {\n                        operations.fit = value;\n                    }\n                });\n            }\n            else if (operation === \"output\" && params) {\n                params.split(\",\").forEach((param) => {\n                    const [key, value] = param.split(\":\");\n                    if (key === \"format\") {\n                        operations.format = value;\n                    }\n                });\n            }\n            else if (operation === \"auto_image\") {\n                operations.format = \"auto\";\n            }\n        });\n    }\n    return {\n        src: `https://${region}.graphassets.com/${envId}/${handle}`,\n        operations,\n        options: {\n            region,\n            envId,\n            handle,\n        },\n    };\n};\nexport const generate = (src, operations, options = {}) => {\n    // First extract the components from the source URL\n    const extracted = extract(src);\n    if (!extracted) {\n        throw new Error(\"Invalid Hygraph URL\");\n    }\n    // Merge options\n    const { region, envId, handle } = {\n        ...extracted.options,\n        ...options,\n    };\n    const transforms = [];\n    // Add resize transformation if width or height is specified\n    if (operations.width || operations.height) {\n        const resize = [];\n        // Always include fit:crop when both dimensions are specified to maintain aspect ratio\n        if (operations.width && operations.height) {\n            resize.push(\"fit:crop\");\n        }\n        else if (operations.fit) {\n            resize.push(`fit:${operations.fit}`);\n        }\n        if (operations.width)\n            resize.push(`width:${operations.width}`);\n        if (operations.height)\n            resize.push(`height:${operations.height}`);\n        if (resize.length)\n            transforms.push(`resize=${resize.join(\",\")}`);\n    }\n    // Add format transformation\n    if (operations.format === \"auto\" ||\n        (!operations.format && !extracted.operations.format)) {\n        transforms.push(\"auto_image\");\n    }\n    else if (operations.format) {\n        transforms.push(`output=format:${operations.format}`);\n    }\n    // Construct the URL in parts\n    const baseUrl = `https://${region}.graphassets.com/${envId}`;\n    const transformPart = transforms.length > 0 ? \"/\" + transforms.join(\"/\") : \"\";\n    const finalUrl = toUrl(`${baseUrl}${transformPart}/${handle}`);\n    return toCanonicalUrlString(finalUrl);\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n        format: \"f\",\n    },\n    defaults: {\n        m: \"cropbox\",\n    },\n    kvSeparator: \"_\",\n    paramSeparator: \"/\",\n});\nexport const generate = (src, operations) => {\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(src);\n    url.searchParams.set(\"imgeng\", modifiers);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const imgeng = parsedUrl.searchParams.get(\"imgeng\");\n    if (!imgeng) {\n        return null;\n    }\n    const operations = operationsParser(imgeng);\n    parsedUrl.searchParams.delete(\"imgeng\");\n    return {\n        src: toCanonicalUrlString(parsedUrl),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n        format: \"f\",\n        quality: \"q\",\n    },\n    defaults: {\n        c: \"maintain_ratio\",\n        fo: \"auto\",\n    },\n    kvSeparator: \"-\",\n    paramSeparator: \",\",\n});\nexport const generate = (src, operations) => {\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(src);\n    url.searchParams.set(\"tr\", modifiers);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    let trPart = null;\n    let path = parsedUrl.pathname;\n    // Check for query parameter format\n    if (parsedUrl.searchParams.has(\"tr\")) {\n        trPart = parsedUrl.searchParams.get(\"tr\");\n        parsedUrl.searchParams.delete(\"tr\");\n    }\n    else {\n        // Check for path-based format\n        const pathParts = parsedUrl.pathname.split(\"/\");\n        const trIndex = pathParts.findIndex((part) => part.startsWith(\"tr:\"));\n        if (trIndex !== -1) {\n            trPart = pathParts[trIndex].slice(3); // Remove 'tr:' prefix\n            path = pathParts.slice(0, trIndex).concat(pathParts.slice(trIndex + 1)).join(\"/\");\n        }\n    }\n    if (!trPart) {\n        return null;\n    }\n    parsedUrl.pathname = path;\n    const operations = operationsParser(trPart);\n    return {\n        src: toCanonicalUrlString(parsedUrl),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        format: \"fm\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n    defaults: {\n        fit: \"min\",\n        auto: \"format\",\n    },\n});\nexport const extract = (url) => {\n    const src = toUrl(url);\n    const operations = operationsParser(url);\n    src.search = \"\";\n    return { src: toCanonicalUrlString(src), operations };\n};\nexport const generate = (src, operations) => {\n    const modifiers = operationsGenerator(operations);\n    const url = toUrl(src);\n    url.search = modifiers;\n    if (url.searchParams.has(\"fm\") && url.searchParams.get(\"auto\") === \"format\") {\n        url.searchParams.delete(\"auto\");\n    }\n    return toCanonicalUrlString(url);\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createOperationsHandlers, stripLeadingSlash, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n        format: \"f\",\n    },\n    defaults: {\n        f: \"auto\",\n    },\n    kvSeparator: \"_\",\n    paramSeparator: \",\",\n});\nexport const generate = (src, operations, options) => {\n    if (operations.width && operations.height) {\n        operations.s = `${operations.width}x${operations.height}`;\n        delete operations.width;\n        delete operations.height;\n    }\n    const modifiers = operationsGenerator(operations);\n    const baseURL = options?.baseURL ?? \"/_ipx\";\n    const url = toUrl(baseURL);\n    url.pathname = `${url.pathname}/${modifiers}/${stripLeadingSlash(src.toString())}`;\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const [, baseUrlPart, modifiers, ...srcParts] = parsedUrl.pathname.split(\"/\");\n    if (!modifiers || !srcParts.length) {\n        return null;\n    }\n    const operations = operationsParser(modifiers);\n    // Handle the 's' parameter\n    if (operations.s) {\n        const [width, height] = operations.s.split(\"x\").map(Number);\n        operations.width = width;\n        operations.height = height;\n        delete operations.s;\n    }\n    return {\n        src: \"/\" + srcParts.join(\"/\"),\n        operations,\n        options: {\n            baseURL: `${parsedUrl.origin}/${baseUrlPart}`,\n        },\n    };\n};\nexport const transform = (src, operations, options) => {\n    const url = toUrl(src);\n    const baseURL = options?.baseURL;\n    if ((baseURL && url.toString().startsWith(baseURL)) ||\n        url.pathname.startsWith(\"/_ipx\")) {\n        const extracted = extract(src);\n        if (extracted) {\n            return generate(extracted.src, { ...extracted.operations, ...operations }, { baseURL: extracted.options.baseURL });\n        }\n    }\n    return generate(src, operations, { baseURL });\n};\n", "import { createExtractAndGenerate, createOperationsHandlers, paramToBoolean, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst BOOLEAN_PARAMS = [\n    \"enlarge\",\n    \"flip\",\n    \"flop\",\n    \"negate\",\n    \"normalize\",\n    \"grayscale\",\n    \"removealpha\",\n    \"olrepeat\",\n    \"progressive\",\n    \"adaptive\",\n    \"lossless\",\n    \"nearlossless\",\n    \"metadata\",\n];\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    defaults: {\n        fit: \"cover\",\n    },\n    formatMap: {\n        jpg: \"jpeg\",\n    },\n});\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    for (const key of BOOLEAN_PARAMS) {\n        if (operations[key] !== undefined) {\n            operations[key] = operations[key] ? 1 : 0;\n        }\n    }\n    url.search = operationsGenerator(operations);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    for (const key of BOOLEAN_PARAMS) {\n        if (operations[key] !== undefined) {\n            operations[key] = paramToBoolean(operations[key]);\n        }\n    }\n    parsedUrl.search = \"\";\n    return {\n        src: toCanonicalUrlString(parsedUrl),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, paramToBoolean, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    formatMap: {\n        jpg: \"jpeg\",\n    },\n    keyMap: {\n        format: \"fm\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n});\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    if (operations.lossless !== undefined) {\n        operations.lossless = operations.lossless ? 1 : 0;\n    }\n    if (operations.width && operations.height) {\n        operations.fit = \"crop\";\n    }\n    url.search = operationsGenerator(operations);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    if (operations.lossless !== undefined) {\n        operations.lossless = paramToBoolean(operations.lossless);\n    }\n    parsedUrl.search = \"\";\n    return {\n        src: toCanonicalUrlString(parsedUrl),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { getProviderForUrlByPath } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    defaults: {\n        fit: \"cover\",\n    },\n    keyMap: {\n        format: \"fm\",\n        width: \"w\",\n        height: \"h\",\n        quality: \"q\",\n    },\n});\nexport const generate = (src, operations, options = {}) => {\n    const url = toUrl(`${options.baseUrl || \"\"}/.netlify/images`);\n    url.search = operationsGenerator(operations);\n    url.searchParams.set(\"url\", src.toString());\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    if (getProviderForUrlByPath(url) !== \"netlify\") {\n        return null;\n    }\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    // deno-lint-ignore no-explicit-any\n    delete operations.url;\n    const sourceUrl = parsedUrl.searchParams.get(\"url\") || \"\";\n    parsedUrl.search = \"\";\n    return {\n        src: sourceUrl,\n        operations,\n        options: {\n            baseUrl: parsedUrl.hostname === \"n\" ? undefined : parsedUrl.origin,\n        },\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { getProviderForUrlByPath } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        quality: \"q\",\n        height: false,\n        format: false,\n    },\n    defaults: {\n        q: 75,\n    },\n});\nexport const generate = (src, operations, options = {}) => {\n    const url = toUrl(`${options.baseUrl || \"\"}/${options.prefix || \"_vercel\"}/image`);\n    url.search = operationsGenerator(operations);\n    url.searchParams.append(\"url\", src.toString());\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url, options = {}) => {\n    if (![\"vercel\", \"nextjs\"].includes(getProviderForUrlByPath(url) || \"\")) {\n        return null;\n    }\n    const parsedUrl = toUrl(url);\n    const sourceUrl = parsedUrl.searchParams.get(\"url\") || \"\";\n    parsedUrl.searchParams.delete(\"url\");\n    const operations = operationsParser(parsedUrl);\n    parsedUrl.search = \"\";\n    return {\n        src: sourceUrl,\n        operations,\n        options: {\n            baseUrl: options.baseUrl ?? parsedUrl.origin,\n        },\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { extract as vercelExtract, generate as vercelGenerate, } from \"./vercel.js\";\nimport { createExtractAndGenerate } from \"../utils.js\";\nexport const generate = (src, operations, options = {}) => vercelGenerate(src, operations, { ...options, prefix: \"_next\" });\nexport const extract = (url, options) => vercelExtract(url, options);\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { getProviderForUrl } from \"../detect.js\";\nimport { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"wid\",\n        height: \"hei\",\n        quality: \"qlt\",\n        format: \"fmt\",\n    },\n    defaults: {\n        fit: \"crop,0\",\n    },\n});\nconst BASE = \"https://s7d1.scene7.com/is/image/\";\nexport const generate = (src, operations) => {\n    const url = new URL(src, BASE);\n    url.search = operationsGenerator(operations);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    if (getProviderForUrl(url) !== \"scene7\") {\n        return null;\n    }\n    const parsedUrl = new URL(url, BASE);\n    const operations = operationsParser(parsedUrl);\n    parsedUrl.search = \"\";\n    return {\n        src: parsedUrl.toString(),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst shopifyRegex = /(.+?)(?:_(?:(pico|icon|thumb|small|compact|medium|large|grande|original|master)|(\\d*)x(\\d*)))?(?:_crop_([a-z]+))?(\\.[a-zA-Z]+)(\\.png|\\.jpg|\\.webp|\\.avif)?$/;\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        format: false,\n    },\n});\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    const basePath = url.pathname.replace(shopifyRegex, \"$1$6\");\n    // Update pathname with the clean version (remove size details)\n    url.pathname = basePath;\n    // Add query parameters for size, format, etc.\n    url.search = operationsGenerator(operations);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const match = shopifyRegex.exec(parsedUrl.pathname);\n    const operations = operationsParser(parsedUrl);\n    if (match) {\n        const [, , , width, height, crop] = match;\n        if (width && height && !operations.width && !operations.height) {\n            operations.width = parseInt(width, 10);\n            operations.height = parseInt(height, 10);\n        }\n        if (crop) {\n            operations.crop ??= crop;\n        }\n    }\n    const basePath = parsedUrl.pathname.replace(shopifyRegex, \"$1$6\");\n    parsedUrl.pathname = basePath;\n    for (const key of [\"width\", \"height\", \"crop\", \"pad_color\", \"format\"]) {\n        parsedUrl.searchParams.delete(key);\n    }\n    return {\n        src: parsedUrl.toString(),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst storyBlokAssets = /(?<id>\\/f\\/\\d+\\/\\d+x\\d+\\/\\w+\\/[^\\/]+)\\/?(?<modifiers>m\\/?(?<crop>\\d+x\\d+:\\d+x\\d+)?\\/?(?<resize>(?<flipx>\\-)?(?<width>\\d+)x(?<flipy>\\-)?(?<height>\\d+))?\\/?(filters\\:(?<filters>[^\\/]+))?)?$/;\nconst storyBlokImg2 = /^(?<modifiers>\\/(?<crop>\\d+x\\d+:\\d+x\\d+)?\\/?(?<resize>(?<flipx>\\-)?(?<width>\\d+)x(?<flipy>\\-)?(?<height>\\d+))?\\/?(filters\\:(?<filters>[^\\/]+))?\\/?)?(?<id>\\/f\\/.+)$/;\nconst splitFilters = (filters) => {\n    if (!filters) {\n        return {};\n    }\n    return Object.fromEntries(filters.split(\":\").map((filter) => {\n        if (!filter)\n            return [];\n        const [key, value] = filter.split(\"(\");\n        return [key, value.replace(\")\", \"\")];\n    }));\n};\nconst generateFilters = (filters) => {\n    if (!filters) {\n        return undefined;\n    }\n    const filterItems = Object.entries(filters).map(([key, value]) => `${key}(${value ?? \"\"})`);\n    if (filterItems.length === 0) {\n        return undefined;\n    }\n    return `filters:${filterItems.join(\":\")}`;\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const regex = parsedUrl.hostname === \"img2.storyblok.com\"\n        ? storyBlokImg2\n        : storyBlokAssets;\n    const matches = regex.exec(parsedUrl.pathname);\n    if (!matches || !matches.groups) {\n        return null;\n    }\n    const { id, crop, width, height, filters, flipx, flipy } = matches.groups;\n    const { format, ...filterMap } = splitFilters(filters ?? \"\");\n    // We update old img2.storyblok.com URLs to use the new syntax and domain\n    if (parsedUrl.hostname === \"img2.storyblok.com\") {\n        parsedUrl.hostname = \"a.storyblok.com\";\n    }\n    const operations = Object.fromEntries([\n        [\"width\", Number(width) || undefined],\n        [\"height\", Number(height) || undefined],\n        [\"format\", format],\n        [\"crop\", crop],\n        [\"filters\", filterMap],\n        [\"flipx\", flipx],\n        [\"flipy\", flipy],\n    ].filter(([_, value]) => value !== undefined));\n    return {\n        src: `${parsedUrl.origin}${id}`,\n        operations,\n    };\n};\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    const { width = 0, height = 0, format, crop, filters = {}, flipx = \"\", flipy = \"\", } = operations;\n    const size = `${flipx}${width}x${flipy}${height}`;\n    if (format) {\n        filters.format = format;\n    }\n    const parts = [\n        url.pathname,\n        \"m\",\n        crop,\n        size,\n        generateFilters(filters),\n    ].filter(Boolean);\n    url.pathname = parts.join(\"/\");\n    return toCanonicalUrlString(url);\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst STORAGE_URL_PREFIX = \"/storage/v1/object/public/\";\nconst RENDER_URL_PREFIX = \"/storage/v1/render/image/public/\";\nconst isRenderUrl = (url) => url.pathname.startsWith(RENDER_URL_PREFIX);\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({});\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    const basePath = url.pathname.replace(RENDER_URL_PREFIX, STORAGE_URL_PREFIX);\n    // Update the pathname with the cleaned version\n    url.pathname = basePath;\n    // Supabase uses auto-format unless set to origin. Specific formats are not supported\n    if (operations.format && operations.format !== \"origin\") {\n        delete operations.format;\n    }\n    // Add query parameters for image transformation\n    url.search = operationsGenerator(operations);\n    // Replace with the render prefix for rendering\n    return toCanonicalUrlString(url).replace(STORAGE_URL_PREFIX, RENDER_URL_PREFIX);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    const isRender = isRenderUrl(parsedUrl);\n    const imagePath = parsedUrl.pathname.replace(RENDER_URL_PREFIX, \"\").replace(STORAGE_URL_PREFIX, \"\");\n    if (!isRender) {\n        return {\n            src: toCanonicalUrlString(parsedUrl),\n            operations,\n        };\n    }\n    return {\n        src: `${parsedUrl.origin}${STORAGE_URL_PREFIX}${imagePath}`,\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { stripTrailingSlash } from \"../utils.js\";\nimport { addTrailingSlash, createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst uploadcareRegex = /^https?:\\/\\/(?<host>[^\\/]+)\\/(?<uuid>[^\\/]+)(?:\\/(?<filename>[^\\/]+)?)?/;\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: false,\n        height: false,\n    },\n    defaults: {\n        format: \"auto\",\n    },\n    kvSeparator: \"/\",\n    paramSeparator: \"/-/\",\n});\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const match = uploadcareRegex.exec(parsedUrl.toString());\n    if (!match || !match.groups) {\n        return null;\n    }\n    const { host, uuid } = match.groups;\n    const [, ...operationsString] = parsedUrl.pathname.split(\"/-/\");\n    const operations = operationsParser(operationsString.join(\"/-/\") || \"\");\n    if (operations.resize) {\n        const [width, height] = operations.resize.split(\"x\");\n        if (width)\n            operations.width = parseInt(width);\n        if (height)\n            operations.height = parseInt(height);\n        delete operations.resize;\n    }\n    return {\n        src: `https://${host}/${uuid}/`,\n        operations,\n        options: { host },\n    };\n};\nexport const generate = (src, operations, options = {}) => {\n    const url = toUrl(src);\n    const host = options.host || url.hostname;\n    // Strip filename from the URL\n    const match = uploadcareRegex.exec(url.toString());\n    if (match?.groups) {\n        url.pathname = `/${match.groups.uuid}/`;\n    }\n    operations.resize = operations.resize ||\n        `${operations.width ?? \"\"}x${operations.height ?? \"\"}`;\n    delete operations.width;\n    delete operations.height;\n    const modifiers = addTrailingSlash(operationsGenerator(operations));\n    url.hostname = host;\n    url.pathname = stripTrailingSlash(url.pathname) +\n        (modifiers ? `/-/${modifiers}` : \"\") + (match?.groups?.filename ?? \"\");\n    return toCanonicalUrlString(url);\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { createExtractAndGenerate, createOperationsHandlers, toCanonicalUrlString, toUrl, } from \"../utils.js\";\nconst { operationsGenerator, operationsParser } = createOperationsHandlers({\n    keyMap: {\n        width: \"w\",\n        height: \"h\",\n    },\n    defaults: {\n        crop: \"1\",\n    },\n});\nexport const generate = (src, operations) => {\n    const url = toUrl(src);\n    const { crop } = operations;\n    if (typeof crop !== \"undefined\" && crop !== \"0\") {\n        operations.crop = crop ? \"1\" : \"0\";\n    }\n    url.search = operationsGenerator(operations);\n    return toCanonicalUrlString(url);\n};\nexport const extract = (url) => {\n    const parsedUrl = toUrl(url);\n    const operations = operationsParser(parsedUrl);\n    if (operations.crop !== undefined) {\n        operations.crop = operations.crop === \"1\";\n    }\n    parsedUrl.search = \"\";\n    return {\n        src: toCanonicalUrlString(parsedUrl),\n        operations,\n    };\n};\nexport const transform = createExtractAndGenerate(extract, generate);\n", "import { getProviderForUrl } from \"./detect.js\";\nimport { transform as appwrite } from \"./providers/appwrite.js\";\nimport { transform as astro } from \"./providers/astro.js\";\nimport { transform as builderio } from \"./providers/builder.io.js\";\nimport { transform as bunny } from \"./providers/bunny.js\";\nimport { transform as cloudflare } from \"./providers/cloudflare.js\";\nimport { transform as cloudflare_images } from \"./providers/cloudflare_images.js\";\nimport { transform as cloudimage } from \"./providers/cloudimage.js\";\nimport { transform as cloudinary } from \"./providers/cloudinary.js\";\nimport { transform as contentful } from \"./providers/contentful.js\";\nimport { transform as contentstack } from \"./providers/contentstack.js\";\nimport { transform as directus } from \"./providers/directus.js\";\nimport { transform as hygraph } from \"./providers/hygraph.js\";\nimport { transform as imageengine } from \"./providers/imageengine.js\";\nimport { transform as imagekit } from \"./providers/imagekit.js\";\nimport { transform as imgix } from \"./providers/imgix.js\";\nimport { transform as ipx } from \"./providers/ipx.js\";\nimport { transform as keycdn } from \"./providers/keycdn.js\";\nimport { transform as kontentai } from \"./providers/kontent.ai.js\";\nimport { transform as netlify } from \"./providers/netlify.js\";\nimport { transform as nextjs } from \"./providers/nextjs.js\";\nimport { transform as scene7 } from \"./providers/scene7.js\";\nimport { transform as shopify } from \"./providers/shopify.js\";\nimport { transform as storyblok } from \"./providers/storyblok.js\";\nimport { transform as supabase } from \"./providers/supabase.js\";\nimport { transform as uploadcare } from \"./providers/uploadcare.js\";\nimport { transform as vercel } from \"./providers/vercel.js\";\nimport { transform as wordpress } from \"./providers/wordpress.js\";\nconst transformerMap = {\n    appwrite,\n    astro,\n    \"builder.io\": builderio,\n    bunny,\n    cloudflare,\n    cloudflare_images,\n    cloudimage,\n    cloudinary,\n    contentful,\n    contentstack,\n    directus,\n    hygraph,\n    imageengine,\n    imagekit,\n    imgix,\n    ipx,\n    keycdn,\n    \"kontent.ai\": kontentai,\n    netlify,\n    nextjs,\n    scene7,\n    shopify,\n    storyblok,\n    supabase,\n    uploadcare,\n    vercel,\n    wordpress,\n};\n/**\n * Returns a transformer function if the given CDN is supported\n */\nexport function getTransformerForCdn(cdn) {\n    if (!cdn) {\n        return undefined;\n    }\n    return transformerMap[cdn];\n}\n/**\n * Transforms an image URL to a new URL with the given options.\n * If the URL is not from a known image CDN it returns undefined.\n */\nexport function transformUrl({ url, provider, cdn: cdnOption, fallback, width, height, format, quality, }, providerOperations, providerOptions) {\n    const cdn = provider || cdnOption ||\n        getProviderForUrl(url) || fallback;\n    if (!cdn) {\n        return undefined;\n    }\n    return getTransformerForCdn(cdn)?.(url, {\n        ...{ width, height, format, quality },\n        ...providerOperations?.[cdn],\n    }, providerOptions?.[cdn] ?? {});\n}\n", "import {\n  DEFAULT_RESOLUTIONS,\n  getBreakpoints,\n  getSizes,\n  getSrcSet,\n  getSrcSetEntries,\n  getStyle,\n  inferImageDimensions,\n  normalizeImageType,\n  transformBaseImageProps,\n  transformBaseSourceProps\n} from \"./chunk-7DG3H6KO.mjs\";\n\n// src/auto.ts\nimport {\n  getProviderForUrl,\n  getTransformerForCdn\n} from \"unpic\";\nfunction transformProps({\n  cdn,\n  fallback,\n  operations = {},\n  options,\n  ...props\n}) {\n  cdn ??= getProviderForUrl(props.src) || fallback;\n  if (!cdn) {\n    return props;\n  }\n  const transformer = getTransformerForCdn(cdn);\n  if (!transformer) {\n    return props;\n  }\n  return transformBaseImageProps({\n    ...props,\n    operations: operations?.[cdn],\n    options: options?.[cdn],\n    transformer\n  });\n}\nfunction transformSourceProps({\n  cdn,\n  fallback,\n  operations,\n  options,\n  ...props\n}) {\n  cdn ??= getProviderForUrl(props.src) || fallback;\n  if (!cdn) {\n    return props;\n  }\n  const transformer = getTransformerForCdn(cdn);\n  if (!transformer) {\n    return props;\n  }\n  return transformBaseSourceProps({\n    ...props,\n    operations: operations?.[cdn],\n    options: options?.[cdn],\n    transformer\n  });\n}\nexport {\n  DEFAULT_RESOLUTIONS,\n  getBreakpoints,\n  getSizes,\n  getSrcSet,\n  getSrcSetEntries,\n  getStyle,\n  inferImageDimensions,\n  normalizeImageType,\n  transformProps,\n  transformSourceProps\n};\n"], "mappings": ";;;;;;;;;;;AACA,YAAuB;AACvB,IAAI,aAA6B,oBAAI,IAAI,CAAC,OAAO,CAAC;AAClD,IAAI,aAAa,SAAS;AAC1B,IAAI,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,eAAe,aAAa,kBAAkB;AAChD;AACA,IAAI,WAAW,CAAC,QAAQ;AACtB,MAAI,IAAI,WAAW,OAAO,KAAK,IAAI,WAAW,OAAO,GAAG;AACtD,WAAO;AAAA,EACT;AACA,SAAO,SAAS,GAAG,KAAK,IAAI,QAAQ,OAAO,CAAC,WAAW,OAAO,CAAC,EAAE,YAAY,CAAC;AAChF;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;AAAA,MACpC,SAAS,CAAC;AAAA,MACV,WAAW,IAAI,CAAC,KAAK,KAAK,OAAO,MAAM,WAAW,cAAc,CAAC,IAAI;AAAA,IACvE,CAAC;AAAA,EACH;AACF;;;AChBA,IAAAA,SAAuB;;;ACJvB,IAAI,WAAW,CAAC,OAAO,WAAW;AAChC,MAAI,CAAC,SAAS,CAAC,QAAQ;AACrB,WAAO;AAAA,EACT;AACA,UAAQ,QAAQ;AAAA;AAAA;AAAA,IAGd,KAAK;AACH,aAAO,eAAe,KAAK,OAAO,KAAK;AAAA;AAAA,IAEzC,KAAK;AACH,aAAO,GAAG,KAAK;AAAA;AAAA,IAEjB,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,IAAI,WAAW,CAAC,UAAU,SAAS,UAAU,IAAI,GAAG,KAAK,OAAO;AAChE,IAAI,WAAW,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ;AACF,MAAM;AACJ,QAAM,eAAe;AAAA,IACnB,CAAC,cAAc,SAAS;AAAA,EAC1B;AACA,MAAI,YAAY,WAAW,QAAQ,KAAK,YAAY,WAAW,OAAO,KAAK,YAAY,WAAW,OAAO,KAAK,YAAY,WAAW,GAAG,GAAG;AACzI,iBAAa,KAAK,CAAC,oBAAoB,OAAO,UAAU,GAAG,CAAC;AAC5D,iBAAa,KAAK,CAAC,mBAAmB,OAAO,CAAC;AAC9C,iBAAa,KAAK,CAAC,qBAAqB,WAAW,CAAC;AAAA,EACtD,OAAO;AACL,iBAAa,KAAK,CAAC,cAAc,UAAU,CAAC;AAAA,EAC9C;AACA,MAAI,WAAW,SAAS;AACtB,iBAAa,KAAK,CAAC,SAAS,SAAS,KAAK,CAAC,CAAC;AAC5C,iBAAa,KAAK,CAAC,UAAU,SAAS,MAAM,CAAC,CAAC;AAAA,EAChD;AACA,MAAI,WAAW,eAAe;AAC5B,iBAAa,KAAK,CAAC,aAAa,SAAS,KAAK,CAAC,CAAC;AAChD,iBAAa,KAAK,CAAC,cAAc,SAAS,MAAM,CAAC,CAAC;AAClD,iBAAa,KAAK;AAAA,MAChB;AAAA,MACA,cAAc,GAAG,WAAW,KAAK;AAAA,IACnC,CAAC;AACD,iBAAa,KAAK,CAAC,SAAS,MAAM,CAAC;AAAA,EACrC;AACA,MAAI,WAAW,aAAa;AAC1B,iBAAa,KAAK,CAAC,SAAS,MAAM,CAAC;AACnC,iBAAa,KAAK;AAAA,MAChB;AAAA,MACA,cAAc,GAAG,WAAW,KAAK;AAAA,IACnC,CAAC;AACD,iBAAa,KAAK,CAAC,UAAU,SAAS,MAAM,CAAC,CAAC;AAAA,EAChD;AACA,SAAO,OAAO;AAAA,IACZ,aAAa,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK;AAAA,EAC1C;AACF;AACA,IAAI,sBAAsB;AAAA,EACxB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAEF;AACA,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,CAAC;AAAA,EACpB;AAAA,EACA;AAAA,EACA,cAAc;AAChB,MAAM;AACJ,MAAI,WAAW,aAAa;AAC1B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,OAAO;AACV,WAAO,CAAC;AAAA,EACV;AACA,QAAM,cAAc,QAAQ;AAC5B,MAAI,WAAW,SAAS;AACtB,WAAO,CAAC,OAAO,WAAW;AAAA,EAC5B;AACA,MAAI,WAAW,eAAe;AAC5B,WAAO;AAAA;AAAA,MAEL;AAAA,MACA;AAAA;AAAA,MAEA,GAAG,YAAY,OAAO,CAAC,MAAM,IAAI,WAAW;AAAA,IAC9C;AAAA,EACF;AACA,SAAO,CAAC;AACV;AACA,IAAI,mBAAmB,CAAC;AAAA,EACtB;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,kBAAgB,eAAe,EAAE,OAAO,OAAO,CAAC;AAChD,SAAO,YAAY,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO;AACnD,QAAI;AACJ,QAAI,UAAU,aAAa;AACzB,0BAAoB,KAAK,MAAM,KAAK,WAAW;AAAA,IACjD;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAI,YAAY,CAAC,YAAY;AAC3B,MAAI,EAAE,KAAK,aAAa,WAAW,IAAI;AACvC,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,GAAGC,YAAU,MAAM;AACjE,UAAM,MAAM;AAAA,MACV;AAAA,MACA,EAAE,GAAG,YAAY,GAAGA,YAAU;AAAA,MAC9B,QAAQ;AAAA,IACV;AACA,WAAO,GAAG,KAAK,SAAS,CAAC,IAAIA,YAAU,KAAK;AAAA,EAC9C,CAAC,EAAE,KAAK,KAAK;AACf;AACA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA,GAAG;AACL,GAAG;AACD,UAAQ,SAAS,OAAO,KAAK,KAAK;AAClC,WAAS,UAAU,OAAO,MAAM,KAAK;AACrC,MAAI,UAAU;AACZ,UAAM,YAAY;AAClB,UAAM,kBAAkB;AAAA,EAC1B,OAAO;AACL,UAAM,YAAY;AAClB,UAAM,aAAa;AAAA,EACrB;AACA,MAAI,MAAM,QAAQ,IAAI;AACpB,UAAM,SAAS;AAAA,EACjB;AACA,MAAI,aAAa;AACf,QAAI,OAAO;AACT,UAAI,QAAQ;AAAA,MACZ,OAAO;AACL,iBAAS,KAAK,MAAM,QAAQ,WAAW;AAAA,MACzC;AAAA,IACF,WAAW,QAAQ;AACjB,cAAQ,KAAK,MAAM,SAAS,WAAW;AAAA,IACzC,WAAW,WAAW,aAAa;AAAA,IACnC;AAAA,EACF,WAAW,SAAS,QAAQ;AAC1B,kBAAc,QAAQ;AAAA,EACxB,WAAW,WAAW,aAAa;AAAA,EACnC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AACA,SAAS,wBAAwB,OAAO;AACtC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI,qBAAqB,KAAK;AAC9B,MAAI,eAAe,eAAe,QAAQ;AACxC,UAAM,eAAe,cAAc,KAAK,MAAM,gBAAgB,WAAW,IAAI;AAC7E,UAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,IACF;AACA,QAAI,aAAa;AACf,mBAAa,YAAY,SAAS;AAAA,IACpC;AAAA,EACF;AACA,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,mBAAiB,UAAU,SAAS,OAAO,MAAM;AACjD,MAAI,CAAC,UAAU;AACb,qBAAiB,QAAQ;AAAA,MACvB,GAAG,SAAS,UAAU;AAAA,MACtB,GAAG,iBAAiB;AAAA,IACtB;AAAA,EACF;AACA,MAAI,aAAa;AACf,qBAAiB,SAAS,UAAU;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,cAAc;AAAA,MAClB;AAAA,MACA,EAAE,GAAG,YAAY,OAAO,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,QAAI,aAAa;AACf,YAAM;AAAA,IACR;AACA,QAAI,WAAW,eAAe,WAAW,eAAe;AACtD,cAAQ;AACR,eAAS;AAAA,IACX;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK,KAAK,SAAS;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,CAAC,MAAM;AACT,WAAO,CAAC;AAAA,EACV;AACA,MAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,WAAO;AAAA,MACL,QAAQ,KAAK,MAAM,CAAC;AAAA,MACpB,UAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU,SAAS,SAAS,QAAQ,SAAS,IAAI;AAAA,EACnD;AACF;AACA,SAAS,yBAAyB;AAAA,EAChC;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI,qBAAqB,KAAK;AAC9B,MAAI,CAAC,aAAa;AAChB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,EAAE,QAAQ,SAAS,IAAI,mBAAmB,IAAI;AACpD,YAAU,SAAS,OAAO,MAAM;AAChC,QAAM,SAAS,UAAU;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,cAAc;AAAA,IAClB;AAAA,IACA,EAAE,GAAG,YAAY,OAAO,OAAO;AAAA,IAC/B;AAAA,EACF;AACA,MAAI,aAAa;AACf,UAAM;AAAA,EACR;AACA,QAAM,eAAe;AAAA,IACnB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF;AACA,MAAI,OAAO;AACT,iBAAa,QAAQ;AAAA,EACvB;AACA,MAAI,UAAU;AACZ,iBAAa,OAAO;AAAA,EACtB;AACA,SAAO;AACT;;;AC/VA,IAAO,kBAAQ;AAAA,EACX,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,qBAAqB;AACzB;;;ACdA,IAAO,qBAAQ;AAAA,EACX,aAAa;AAAA,EACb,UAAU;AAAA,EACV,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,aAAa;AAAA,EACb,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AACvB;;;ACfA,IAAO,gBAAQ;AAAA,EACX,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,wBAAwB;AAC5B;;;ACZO,SAAS,eAAe,OAAO;AAClC,MAAI,CAAC,OAAO;AAER,WAAO;AAAA,EACX;AACA,QAAM,MAAM,OAAO,KAAK;AACxB,MAAI,MAAM,GAAG,GAAG;AAEZ,WAAO;AAAA,EACX;AAEA,SAAO,KAAK,MAAM,GAAG;AACzB;AAIO,IAAM,gBAAgB,CAAC,QAAQ;AAClC,QAAM,EAAE,UAAU,OAAO,IAAI;AAC7B,SAAO,GAAG,QAAQ,GAAG,MAAM;AAC/B;AAIO,IAAM,uBAAuB,CAAC,QAAQ;AACzC,SAAO,IAAI,aAAa,MAAM,cAAc,GAAG,IAAI,IAAI,SAAS;AACpE;AAIO,IAAM,QAAQ,CAAC,KAAK,SAAS;AAChC,SAAO,OAAO,QAAQ,WAAW,IAAI,IAAI,KAAK,QAAQ,WAAW,IAAI;AACzE;AAIO,IAAM,aAAa,CAAC,SAAS,SAAS,MAAM,MAAO,MACtD,KAAK,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,SAAS,GAAG,GAAG;AAC1D,IAAM,oBAAoB,CAAC,QAAQ,KAAK,WAAW,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI;AACzE,IAAM,qBAAqB,CAAC,QAAQ,KAAK,SAAS,GAAG,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI;AAE5E,IAAM,mBAAmB,CAAC,QAAQ,KAAK,SAAS,GAAG,IAAI,MAAM,GAAG,GAAG;AAInE,IAAM,kBAAkB,CAAC,aAAa,mBAAmB;AAC5D,QAAM,qBAAqB,WAAW,WAAW;AACjD,QAAM,yBAAyB,WAAW,cAAc;AACxD,WAAS,OAAO,OAAO;AACnB,WAAO,mBAAmB,KAAK,EAAE,WAAW,aAAa,kBAAkB,EACtE,WAAW,gBAAgB,sBAAsB;AAAA,EAC1D;AACA,WAAS,OAAO,KAAK,OAAO;AACxB,WAAO,GAAG,OAAO,GAAG,CAAC,GAAG,WAAW,GAAG,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,EAC/D;AACA,SAAO,CAAC,eAAe;AACnB,UAAM,MAAM,MAAM,QAAQ,UAAU,IAC9B,aACA,OAAO,QAAQ,UAAU;AAC/B,WAAO,IAAI,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACjC,UAAI,UAAU,UAAa,UAAU,MAAM;AACvC,eAAO,CAAC;AAAA,MACZ;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,eAAO,MAAM,IAAI,CAAC,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO,OAAO,KAAK,KAAK;AAAA,IAC5B,CAAC,EAAE,KAAK,cAAc;AAAA,EAC1B;AACJ;AAIO,IAAM,eAAe,CAAC,aAAa,mBAAmB;AACzD,MAAI,gBAAgB,OAAO,mBAAmB,KAAK;AAC/C,WAAO;AAAA,EACX;AACA,SAAO,CAAC,QAAQ;AACZ,UAAM,YAAY,IAAI,SAAS;AAC/B,WAAO,OAAO,YAAY,UAAU,MAAM,cAAc,EAAE,IAAI,CAAC,SAAS;AACpE,YAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,WAAW;AAC3C,aAAO,CAAC,UAAU,GAAG,GAAG,UAAU,KAAK,CAAC;AAAA,IAC5C,CAAC,CAAC;AAAA,EACN;AACJ;AAIO,SAAS,gBAAgB,YAAY,WAAW,KAAM,YAAY,KAAM;AAC3E,MAAI,EAAE,OAAO,OAAO,IAAI;AACxB,UAAQ,OAAO,KAAK,KAAK;AACzB,WAAS,OAAO,MAAM,KAAK;AAC3B,MAAI,SAAS,QAAQ,UAAU;AAC3B,QAAI,QAAQ;AACR,eAAS,KAAK,MAAM,SAAS,WAAW,KAAK;AAAA,IACjD;AACA,YAAQ;AAAA,EACZ;AACA,MAAI,UAAU,SAAS,WAAW;AAC9B,QAAI,OAAO;AACP,cAAQ,KAAK,MAAM,QAAQ,YAAY,MAAM;AAAA,IACjD;AACA,aAAS;AAAA,EACb;AACA,SAAO,EAAE,OAAO,OAAO;AAC3B;AACO,SAAS,eAAe,KAAK;AAChC,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAa,OAAO,YAAY,UAAU,aAAa,QAAQ,CAAC;AACtE,aAAW,OAAO,CAAC,SAAS,UAAU,SAAS,GAAG;AAC9C,UAAM,QAAQ,WAAW,GAAG;AAC5B,QAAI,OAAO;AACP,YAAM,SAAS,OAAO,KAAK;AAC3B,UAAI,CAAC,MAAM,MAAM,GAAG;AAEhB,mBAAW,GAAG,IAAI;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,YAAU,SAAS;AACnB,SAAO;AAAA,IACH;AAAA,IACA,KAAK,qBAAqB,SAAS;AAAA,EACvC;AACJ;AACO,SAAS,oBAAoB,EAAE,SAAS,CAAC,GAAG,YAAY,CAAC,GAAG,WAAW,CAAC,EAAE,GAAG,YAAY;AAC5F,MAAI,WAAW,UAAU,WAAW,UAAU,WAAW;AACrD,eAAW,SAAS,UAAU,WAAW,MAAM;AAAA,EACnD;AACA,MAAI,WAAW,OAAO;AAClB,eAAW,QAAQ,eAAe,WAAW,KAAK;AAAA,EACtD;AACA,MAAI,WAAW,QAAQ;AACnB,eAAW,SAAS,eAAe,WAAW,MAAM;AAAA,EACxD;AACA,aAAW,KAAK,QAAQ;AACpB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,CAAC,GAAG;AAClD;AAAA,IACJ;AACA,UAAM,MAAM;AACZ,QAAI,OAAO,GAAG,MAAM,OAAO;AACvB,aAAO,WAAW,GAAG;AACrB;AAAA,IACJ;AACA,QAAI,OAAO,GAAG,KAAK,WAAW,GAAG,GAAG;AAChC,iBAAW,OAAO,GAAG,CAAC,IAAI,WAAW,GAAG;AACxC,aAAO,WAAW,GAAG;AAAA,IACzB;AAAA,EACJ;AACA,aAAW,KAAK,UAAU;AACtB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,CAAC,GAAG;AACpD;AAAA,IACJ;AACA,UAAM,MAAM;AACZ,UAAM,QAAQ,SAAS,GAAG;AAC1B,QAAI,CAAC,WAAW,GAAG,KAAK,UAAU,QAAW;AACzC,UAAI,OAAO,GAAG,MAAM,OAAO;AACvB;AAAA,MACJ;AACA,YAAM,cAAc,OAAO,GAAG,KAAK;AACnC,UAAI,eAAe,YAAY;AAC3B;AAAA,MACJ;AAEA,iBAAW,WAAW,IAAI;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,YAAY,CAElB,QAAQ,OAAO,YAAY,OAAO,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/D,SAAS,sBAAsB,EAAE,SAAS,CAAC,GAAG,YAAY,CAAC,GAAG,WAAW,CAAC,EAAE,GAAG,YAAY;AAC9F,QAAM,iBAAiB,UAAU,MAAM;AACvC,QAAM,oBAAoB,UAAU,SAAS;AAC7C,QAAM,MAAM,oBAAoB;AAAA,IAC5B,QAAQ;AAAA,IACR,WAAW;AAAA,IACX;AAAA,EACJ,GAAG,UAAU;AACb,MAAI,IAAI,OAAO;AACX,QAAI,QAAQ,eAAe,IAAI,KAAK;AAAA,EACxC;AACA,MAAI,IAAI,QAAQ;AACZ,QAAI,SAAS,eAAe,IAAI,MAAM;AAAA,EAC1C;AACA,QAAM,IAAI,OAAO,IAAI,OAAO;AAC5B,MAAI,CAAC,MAAM,CAAC,GAAG;AACX,QAAI,UAAU;AAAA,EAClB;AACA,SAAO;AACX;AAEA,IAAM,cAAc,CAAC,QAAQ;AACzB,QAAM,YAAY,MAAM,GAAG;AAC3B,SAAO,OAAO,YAAY,UAAU,aAAa,QAAQ,CAAC;AAC9D;AACO,SAAS,0BAA0B,EAAE,cAAc,KAAK,iBAAiB,KAAK,GAAG,QAAQ,IAAI,CAAC,GAAG;AACpG,QAAM,YAAY,gBAAgB,aAAa,cAAc;AAC7D,SAAO,CAAC,eAAe;AACnB,UAAM,uBAAuB,oBAAoB,SAAS,UAAU;AACpE,WAAO,UAAU,oBAAoB;AAAA,EACzC;AACJ;AACO,SAAS,uBAAuB,EAAE,cAAc,KAAK,iBAAiB,KAAK,UAAU,GAAG,GAAG,QAAQ,IAAI,CAAC,GAAG;AAC9G,QAAM,SAAS,aAAa,aAAa,cAAc;AACvD,SAAO,CAAC,QAAQ;AACZ,UAAM,aAAa,MAAM,OAAO,GAAG,IAAI,CAAC;AACxC,WAAO,sBAAsB,SAAS,UAAU;AAAA,EACpD;AACJ;AACO,SAAS,yBAAyB,QAAQ;AAC7C,QAAMC,wBAAsB,0BAA0B,MAAM;AAC5D,QAAMC,qBAAmB,uBAAuB,MAAM;AACtD,SAAO,EAAE,qBAAAD,uBAAqB,kBAAAC,mBAAiB;AACnD;AACO,SAAS,eAAe,OAAO;AAClC,MAAI,UAAU,UAAa,UAAU,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAO,QAAQ,KAAK,MAAM,OAAO,SAAS,CAAC,CAAC;AAAA,EAChD,QACM;AACF,WAAO,QAAQ,KAAK;AAAA,EACxB;AACJ;AACA,IAAM,kBAAkB,CAAC,QAAQ,OAAO,YAAY,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,UAAU,MAAS,CAAC;AAC3G,SAAS,yBAAyBC,WAASC,YAAU;AACxD,SAAQ,CAAC,KAAK,YAAY,YAAY;AAClC,UAAM,OAAOD,UAAQ,KAAK,OAAO;AACjC,QAAI,CAAC,MAAM;AACP,aAAOC,WAAS,KAAK,YAAY,OAAO;AAAA,IAC5C;AACA,WAAOA,WAAS,KAAK,KAAK;AAAA,MACtB,GAAG,KAAK;AAAA,MACR,GAAG,gBAAgB,UAAU;AAAA,IACjC,GAAG;AAAA;AAAA,MAEC,GAAG,KAAK;AAAA,MACR,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACJ;;;AC9OA,IAAM,aAAa,IAAI,IAAI,OAAO,QAAQ,eAAO,CAAC;AAClD,IAAM,gBAAgB,OAAO,QAAQ,kBAAU;AAC/C,IAAM,WAAW,OAAO,QAAQ,aAAK;AAI9B,SAAS,kBAAkB,KAAK;AACnC,SAAO,0BAA0B,GAAG,KAAK,wBAAwB,GAAG;AACxE;AAKO,SAAS,0BAA0B,KAAK;AAC3C,MAAI,OAAO,QAAQ,YAAY,CAAC,IAAI,WAAW,UAAU,GAAG;AACxD,WAAO;AAAA,EACX;AACA,QAAM,EAAE,SAAS,IAAI,MAAM,GAAG;AAC9B,QAAM,MAAM,WAAW,IAAI,QAAQ;AACnC,MAAI,KAAK;AACL,WAAO;AAAA,EACX;AACA,SAAO,cAAc,KAAK,CAAC,CAAC,SAAS,MAAM,SAAS,SAAS,SAAS,CAAC,IAAI,CAAC,KAAK;AACrF;AAQO,SAAS,wBAAwB,KAAK;AAEzC,QAAM,EAAE,SAAS,IAAI,MAAM,GAAG;AAC9B,SAAO,SAAS,KAAK,CAAC,CAAC,IAAI,MAAM,SAAS,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK;AACxE;;;ACrCA,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,EAAE,qBAAqB,iBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAM,WAAW,CAAC,KAAK,cAAc;AACxC,QAAM,MAAM,MAAM,IAAI,SAAS,EAAE,QAAQ,iBAAiB,kBAAkB,CAAC;AAC7E,QAAM,eAAe,IAAI,aAAa,IAAI,SAAS,KAAK;AACxD,QAAM,aAAa,oBAAoB,SAAS;AAChD,MAAI,SAAS;AACb,MAAI,aAAa,OAAO,WAAW,YAAY;AAC/C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAM,UAAU,CAAC,QAAQ;AAC5B,MAAI,wBAAwB,GAAG,MAAM,YAAY;AAC7C,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAa,iBAAiB,SAAS;AAE7C,SAAO,WAAW;AAClB,QAAM,eAAe,UAAU,aAAa,IAAI,SAAS,KAAK;AAC9D,YAAU,SAAS;AACnB,YAAU,aAAa,OAAO,WAAW,YAAY;AACrD,QAAM,YAAY,UAAU;AAC5B,SAAO;AAAA,IACH,KAAK;AAAA,IACL;AAAA,EACJ;AACJ;AACO,IAAM,YAAY,yBAAyB,SAAS,QAAQ;;;ACnCnE,IAAM,mBAAmB;AACzB,IAAM,EAAE,kBAAAC,mBAAkB,qBAAAC,qBAAoB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AACJ,CAAC;AACM,IAAMC,YAAW,CAAC,KAAK,WAAW,YAAY;AACjD,QAAM,MAAM,MAAM,GAAG,mBAAmB,SAAS,WAAW,EAAE,CAAC,GAAG,SAAS,YAAY,gBAAgB,EAAE;AACzG,QAAM,aAAaD,qBAAoB,SAAS;AAChD,MAAI,SAAS;AACb,MAAI,aAAa,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAME,WAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,MAAM,UAAU,aAAa,IAAI,MAAM;AAC7C,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AACA,YAAU,aAAa,OAAO,MAAM;AACpC,QAAM,aAAaH,kBAAiB,SAAS;AAC7C,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,SAAS,EAAE,SAAS,UAAU,OAAO;AAAA,EACzC;AACJ;AACO,IAAMI,aAAY,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AACxD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,IAAI,cAAc,SAAS,YAAY,mBAAmB;AAC1D,WAAOF,UAAS,KAAK,YAAY,OAAO;AAAA,EAC5C;AACA,QAAM,OAAOC,SAAQ,GAAG;AACxB,MAAI,CAAC,MAAM;AACP,WAAOD,UAAS,KAAK,YAAY,OAAO;AAAA,EAC5C;AACA,UAAQ,YAAY,KAAK,QAAQ;AACjC,SAAOA,UAAS,KAAK,KAAK;AAAA,IACtB,GAAG,KAAK;AAAA,IACR,GAAG;AAAA,EACP,GAAG,OAAO;AACd;;;AC/CA,IAAMG,uBAAsB,0BAA0B;AAAA,EAClD,UAAU;AAAA,IACN,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AACJ,CAAC;AACM,IAAMC,WAAU;AAChB,IAAMC,YAAW,CAAC,KAAK,cAAc;AACxC,QAAM,aAAaF,qBAAoB,SAAS;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,SAAS;AACb,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,aAAY,yBAAyBF,UAASC,SAAQ;;;ACdnE,IAAME,uBAAsB,0BAA0B;AAAA,EAClD,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AACJ,CAAC;AACM,IAAMC,WAAU;AAChB,IAAMC,YAAW,CAAC,KAAK,cAAc;AACxC,QAAM,aAAaF,qBAAoB,SAAS;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,SAAS;AACb,SAAO,qBAAqB,GAAG;AACnC;AACA,IAAM,qBAAqB,yBAAyBC,UAASC,SAAQ;AAC9D,IAAMC,aAAY,CAAC,KAAK,eAAe;AAC1C,QAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,MAAI,SAAS,QAAQ;AACjB,eAAW,iBAAiB,GAAG,KAAK,MAAM,OAAO,KAAK,CAAC,CAAC,IAAI,KAAK,MAAM,OAAO,MAAM,CAAC,CAAC;AAAA,EAC1F;AACA,SAAO,mBAAmB,KAAK,UAAU;AAC7C;;;AClBA,IAAM,EAAE,qBAAAC,sBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,UAAU;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACP,KAAK;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAMC,YAAW,CAAC,KAAK,YAAY,YAAY;AAClD,QAAM,YAAYF,qBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,SAAS,SAAS,WAAW,QAAQ,MAAM,KAAK,GAAG;AACrE,MAAI,WAAW,kBAAkB,SAAS,IAAI,kBAAkB,IAAI,SAAS,CAAC,CAAC;AAC/E,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,WAAU,CAAC,KAAK,YAAY;AACrC,MAAI,wBAAwB,GAAG,MAAM,cAAc;AAC/C,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,CAAC,EAAE,EAAE,EAAE,WAAW,GAAG,GAAG,IAAI,UAAU,SAAS,MAAM,GAAG;AAC9D,QAAM,aAAaF,kBAAiB,SAAS;AAC7C,SAAO;AAAA,IACH,KAAK,qBAAqB,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;AAAA,IAC9C;AAAA,IACA,SAAS;AAAA,MACL,QAAQ,SAAS,WACZ,UAAU,aAAa,MAAM,SAAY,UAAU;AAAA,IAC5D;AAAA,EACJ;AACJ;AACO,IAAMG,aAAY,yBAAyBD,UAASD,SAAQ;;;ACrCnE,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAC3B,IAAM,EAAE,qBAAAG,sBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACD,SAAS,UAAU,SAAS,iBAAiB;AACzC,QAAM,EAAE,MAAM,aAAa,QAAQ,IAAI;AACvC,MAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS;AACnC,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,QAAM,eAAe;AAAA,IACjB;AAAA,IACA,GAAI,SAAS,sBACP,CAAC,IAAI,IACL,CAAC,MAAM,WAAW,eAAe;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,OAAO;AAChB,SAAO,aAAa,KAAK,GAAG;AAChC;AACO,IAAMC,YAAW,CAAC,MAAM,YAAY,UAAU,CAAC,MAAM;AACxD,QAAM,kBAAkBF,qBAAoB,UAAU;AACtD,QAAM,MAAM,UAAU,SAAS,eAAe;AAC9C,SAAO,qBAAqB,MAAM,GAAG,CAAC;AAC1C;AACO,IAAMG,WAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,UAAU;AAAA,IACZ,GAAG,UAAU,SAAS,EAAE,SAAS,qBAAqB;AAAA,IACtD,GAAG,UAAU,SAAS,EAAE,SAAS,kBAAkB;AAAA,EACvD;AACA,MAAI,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACrB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,MAAM,aAAa,SAAS,gBAAgB,IAAI,QAAQ,CAAC,EAAE;AACnE,QAAM,aAAaF,kBAAiB,mBAAmB,EAAE;AACzD,QAAM,UAAU,EAAE,MAAM,aAAa,QAAQ;AAC7C,SAAO;AAAA,IACH,KAAK,UAAU,OAAO;AAAA,IACtB;AAAA,IACA;AAAA,EACJ;AACJ;AACO,IAAMG,aAAY,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AACxD,QAAM,YAAYD,SAAQ,GAAG;AAC7B,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,MAAM,+BAA+B;AAAA,EACnD;AACA,QAAM,gBAAgB,EAAE,GAAG,UAAU,YAAY,GAAG,WAAW;AAC/D,SAAOD,UAAS,UAAU,KAAK,eAAe;AAAA,IAC1C,GAAG,UAAU;AAAA,IACb,GAAG;AAAA,EACP,CAAC;AACL;;;AC9DA,IAAM,EAAE,qBAAAG,sBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,YAAY;AAAA,EAChB;AACJ,CAAC;AACM,IAAMC,YAAW,CAAC,KAAK,YAAY,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,MAAM;AAC7D,MAAI,CAAC,OAAO;AACR,UAAM,IAAI,MAAM,0CAA0C,GAAG;AAAA,EACjE;AACA,MAAI,YAAY,IAAI,SAAS;AAC7B,cAAY,UAAU,QAAQ,gBAAgB,EAAE;AAChD,MAAI,UAAU,SAAS,GAAG,GAAG;AACzB,cAAU,iBAAiB;AAC3B,gBAAY,mBAAmB,SAAS;AAAA,EAC5C;AACA,QAAM,aAAaF,qBAAoB,SAAS;AAChD,QAAM,MAAM,IAAI,IAAI,WAAW,KAAK,eAAe;AACnD,MAAI,WAAW;AACf,MAAI,SAAS;AACb,SAAO,IAAI,SAAS;AACxB;AACO,IAAMG,WAAU,CAAC,KAAK,UAAU,CAAC,MAAM;AAC1C,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,kBAAkB,GAAG,MAAM,cAAc;AACzC,WAAO;AAAA,EACX;AACA,QAAM,aAAaF,kBAAiB,GAAG;AACvC,MAAI,cAAc,IAAI;AACtB,MAAI,WAAW,gBAAgB;AAC3B,kBAAc,mBAAmB,WAAW;AAC5C,WAAO,WAAW;AAAA,EACtB;AACA,UAAQ,UAAU,IAAI,SAAS,QAAQ,gBAAgB,EAAE;AACzD,SAAO;AAAA,IACH,KAAK,GAAG,IAAI,QAAQ,IAAI,WAAW;AAAA,IACnC;AAAA,IACA;AAAA,EACJ;AACJ;AACO,IAAMG,aAAY,yBAAyBD,UAASD,SAAQ;;;AC9CnE,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,EAAE,qBAAAG,sBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,GAAG;AAAA,EACP;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AASD,SAAS,oBAAoB,EAAE,MAAM,WAAW,WAAW,cAAc,WAAW,iBAAiB,SAAS,GAAI,GAAG;AACjH,QAAM,WAAW,SAAS;AAC1B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,WAAW,YAAY;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAC9B;AACA,SAAS,mBAAmB,KAAK;AAC7B,MAAI,UAAU,IAAI,SAAS,EAAE,MAAM,WAAW;AAC9C,MAAI,CAAC,SAAS,QAAQ;AAClB,cAAU,IAAI,SAAS,EAAE,MAAM,YAAY;AAAA,EAC/C;AACA,MAAI,CAAC,SAAS,QAAQ;AAClB,WAAO;AAAA,EACX;AACA,SAAO,QAAQ,UAAU,CAAC;AAC9B;AAmBO,IAAMC,aAAY,CAAC,KAAK,eAAe;AAC1C,QAAM,QAAQ,mBAAmB,IAAI,SAAS,CAAC;AAC/C,MAAI,CAAC,OAAO;AACR,WAAO,IAAI,SAAS;AAAA,EACxB;AACA,QAAM,WAAWC,kBAAiB,MAAM,mBAAmB,EAAE;AAC7D,QAAM,kBAAkBC,qBAAoB;AAAA,IACxC,GAAG;AAAA,IACH,GAAG;AAAA,EACP,CAAC;AACD,SAAO,oBAAoB,KAAK;AACpC;;;AC7EA,IAAMC,uBAAsB,0BAA0B;AAAA,EAClD,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AACJ,CAAC;AACM,IAAMC,YAAW,CAAC,KAAK,cAAc;AACxC,QAAM,aAAaD,qBAAoB,SAAS;AAChD,QAAM,MAAM,IAAI,IAAI,GAAG;AACvB,MAAI,SAAS;AACb,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAME,WAAU;AACvB,IAAMC,sBAAqB,yBAAyBD,UAASD,SAAQ;AAC9D,IAAMG,aAAY,CAAC,KAAK,eAAe;AAC1C,QAAM,EAAE,OAAO,OAAO,IAAI,gBAAgB,YAAY,KAAM,GAAI;AAChE,SAAOD,oBAAmB,KAAK;AAAA,IAC3B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACJ,CAAC;AACL;;;AC1BA,IAAME,wBAAsB,0BAA0B;AAAA,EAClD,UAAU;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACb;AACJ,CAAC;AACM,IAAMC,YAAW,CAAC,KAAK,YAAY,EAAE,UAAU,kCAAkC,IAAI,CAAC,MAAM;AAC/F,MAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,eAAW,QAAQ;AAAA,EACvB;AACA,QAAM,YAAYD,sBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,IAAI,aAAa,KAAK;AACtB,QAAI,WAAW;AACf,QAAI,WAAW,IAAI,IAAI,OAAO,EAAE;AAAA,EACpC;AACA,MAAI,SAAS;AACb,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAME,WAAU,CAAC,QAAQ;AAC5B,QAAM,EAAE,KAAK,WAAW,IAAI,eAAe,GAAG,KAAK,CAAC;AACpD,MAAI,CAAC,cAAc,CAAC,KAAK;AACrB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,OAAO,IAAI,MAAM,GAAG;AAC5B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACL,SAAS;AAAA,IACb;AAAA,EACJ;AACJ;AACO,IAAMC,cAAY,yBAAyBD,UAASD,SAAQ;;;ACjCnE,IAAMG,wBAAsB,0BAA0B;AAAA,EAClD,UAAU;AAAA,IACN,oBAAoB;AAAA,IACpB,KAAK;AAAA,EACT;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,MAAI,MAAM,QAAQ,WAAW,UAAU,GAAG;AACtC,eAAW,aAAa,KAAK,UAAU,WAAW,UAAU;AAAA,EAChE;AACA,QAAM,YAAYD,sBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,SAAS;AACb,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAME,YAAU,CAAC,QAAQ;AAC5B,QAAM,OAAO,eAAe,GAAG;AAC/B,MAAI,MAAM,YAAY,cAClB,OAAO,KAAK,WAAW,eAAe,UAAU;AAChD,QAAI;AACA,WAAK,WAAW,aAAa,KAAK,MAAM,KAAK,WAAW,UAAU;AAAA,IACtE,QACM;AACF,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACO,IAAMC,cAAY,yBAAyBD,WAASD,UAAQ;;;AC5BnE,IAAM,eAAe;AACrB,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,EACT;AACJ,CAAC;AACM,IAAMC,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,UAAU,UAAU,SAAS,EAAE,MAAM,YAAY;AACvD,MAAI,CAAC,SAAS,QAAQ;AAClB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,QAAQ,OAAO,QAAQ,gBAAgB,IAAI,QAAQ;AAE3D,QAAM,aAAa,CAAC;AACpB,MAAI,iBAAiB;AACjB,UAAM,QAAQ,gBAAgB,MAAM,GAAG;AACvC,UAAM,QAAQ,CAAC,SAAS;AACpB,YAAM,CAAC,WAAW,MAAM,IAAI,KAAK,MAAM,GAAG;AAC1C,UAAI,cAAc,YAAY,QAAQ;AAClC,eAAO,MAAM,GAAG,EAAE,QAAQ,CAAC,UAAU;AACjC,gBAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,cAAI,QAAQ,WAAW,QAAQ,UAAU;AACrC,uBAAW,GAAG,IAAI,OAAO,KAAK;AAAA,UAClC,WACS,QAAQ,OAAO;AACpB,uBAAW,MAAM;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL,WACS,cAAc,YAAY,QAAQ;AACvC,eAAO,MAAM,GAAG,EAAE,QAAQ,CAAC,UAAU;AACjC,gBAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,cAAI,QAAQ,UAAU;AAClB,uBAAW,SAAS;AAAA,UACxB;AAAA,QACJ,CAAC;AAAA,MACL,WACS,cAAc,cAAc;AACjC,mBAAW,SAAS;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH,KAAK,WAAW,MAAM,oBAAoB,KAAK,IAAI,MAAM;AAAA,IACzD;AAAA,IACA,SAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,IAAMC,aAAW,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AAEvD,QAAM,YAAYD,UAAQ,GAAG;AAC7B,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACzC;AAEA,QAAM,EAAE,QAAQ,OAAO,OAAO,IAAI;AAAA,IAC9B,GAAG,UAAU;AAAA,IACb,GAAG;AAAA,EACP;AACA,QAAM,aAAa,CAAC;AAEpB,MAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,UAAM,SAAS,CAAC;AAEhB,QAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,aAAO,KAAK,UAAU;AAAA,IAC1B,WACS,WAAW,KAAK;AACrB,aAAO,KAAK,OAAO,WAAW,GAAG,EAAE;AAAA,IACvC;AACA,QAAI,WAAW;AACX,aAAO,KAAK,SAAS,WAAW,KAAK,EAAE;AAC3C,QAAI,WAAW;AACX,aAAO,KAAK,UAAU,WAAW,MAAM,EAAE;AAC7C,QAAI,OAAO;AACP,iBAAW,KAAK,UAAU,OAAO,KAAK,GAAG,CAAC,EAAE;AAAA,EACpD;AAEA,MAAI,WAAW,WAAW,UACrB,CAAC,WAAW,UAAU,CAAC,UAAU,WAAW,QAAS;AACtD,eAAW,KAAK,YAAY;AAAA,EAChC,WACS,WAAW,QAAQ;AACxB,eAAW,KAAK,iBAAiB,WAAW,MAAM,EAAE;AAAA,EACxD;AAEA,QAAM,UAAU,WAAW,MAAM,oBAAoB,KAAK;AAC1D,QAAM,gBAAgB,WAAW,SAAS,IAAI,MAAM,WAAW,KAAK,GAAG,IAAI;AAC3E,QAAM,WAAW,MAAM,GAAG,OAAO,GAAG,aAAa,IAAI,MAAM,EAAE;AAC7D,SAAO,qBAAqB,QAAQ;AACxC;AACO,IAAME,cAAY,yBAAyBF,WAASC,UAAQ;;;ACtGnE,IAAM,EAAE,qBAAAE,uBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,GAAG;AAAA,EACP;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,YAAYF,sBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,aAAa,IAAI,UAAU,SAAS;AACxC,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,SAAS,UAAU,aAAa,IAAI,QAAQ;AAClD,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,QAAM,aAAaF,kBAAiB,MAAM;AAC1C,YAAU,aAAa,OAAO,QAAQ;AACtC,SAAO;AAAA,IACH,KAAK,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;AC/BnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,kBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,GAAG;AAAA,IACH,IAAI;AAAA,EACR;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,YAAYF,sBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,aAAa,IAAI,MAAM,SAAS;AACpC,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,MAAI,SAAS;AACb,MAAI,OAAO,UAAU;AAErB,MAAI,UAAU,aAAa,IAAI,IAAI,GAAG;AAClC,aAAS,UAAU,aAAa,IAAI,IAAI;AACxC,cAAU,aAAa,OAAO,IAAI;AAAA,EACtC,OACK;AAED,UAAM,YAAY,UAAU,SAAS,MAAM,GAAG;AAC9C,UAAM,UAAU,UAAU,UAAU,CAAC,SAAS,KAAK,WAAW,KAAK,CAAC;AACpE,QAAI,YAAY,IAAI;AAChB,eAAS,UAAU,OAAO,EAAE,MAAM,CAAC;AACnC,aAAO,UAAU,MAAM,GAAG,OAAO,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,IACpF;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,YAAU,WAAW;AACrB,QAAM,aAAaF,kBAAiB,MAAM;AAC1C,SAAO;AAAA,IACH,KAAK,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;AChDnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AAAA,EACA,UAAU;AAAA,IACN,KAAK;AAAA,IACL,MAAM;AAAA,EACV;AACJ,CAAC;AACM,IAAMC,YAAU,CAAC,QAAQ;AAC5B,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,aAAaD,mBAAiB,GAAG;AACvC,MAAI,SAAS;AACb,SAAO,EAAE,KAAK,qBAAqB,GAAG,GAAG,WAAW;AACxD;AACO,IAAME,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,YAAYH,sBAAoB,UAAU;AAChD,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,SAAS;AACb,MAAI,IAAI,aAAa,IAAI,IAAI,KAAK,IAAI,aAAa,IAAI,MAAM,MAAM,UAAU;AACzE,QAAI,aAAa,OAAO,MAAM;AAAA,EAClC;AACA,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMI,cAAY,yBAAyBF,WAASC,UAAQ;;;AC3BnE,IAAM,EAAE,qBAAAE,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,GAAG;AAAA,EACP;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,YAAY,YAAY;AAClD,MAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,eAAW,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW,MAAM;AACvD,WAAO,WAAW;AAClB,WAAO,WAAW;AAAA,EACtB;AACA,QAAM,YAAYF,sBAAoB,UAAU;AAChD,QAAM,UAAU,SAAS,WAAW;AACpC,QAAM,MAAM,MAAM,OAAO;AACzB,MAAI,WAAW,GAAG,IAAI,QAAQ,IAAI,SAAS,IAAI,kBAAkB,IAAI,SAAS,CAAC,CAAC;AAChF,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,CAAC,EAAE,aAAa,WAAW,GAAG,QAAQ,IAAI,UAAU,SAAS,MAAM,GAAG;AAC5E,MAAI,CAAC,aAAa,CAAC,SAAS,QAAQ;AAChC,WAAO;AAAA,EACX;AACA,QAAM,aAAaF,mBAAiB,SAAS;AAE7C,MAAI,WAAW,GAAG;AACd,UAAM,CAAC,OAAO,MAAM,IAAI,WAAW,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM;AAC1D,eAAW,QAAQ;AACnB,eAAW,SAAS;AACpB,WAAO,WAAW;AAAA,EACtB;AACA,SAAO;AAAA,IACH,KAAK,MAAM,SAAS,KAAK,GAAG;AAAA,IAC5B;AAAA,IACA,SAAS;AAAA,MACL,SAAS,GAAG,UAAU,MAAM,IAAI,WAAW;AAAA,IAC/C;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,CAAC,KAAK,YAAY,YAAY;AACnD,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,UAAU,SAAS;AACzB,MAAK,WAAW,IAAI,SAAS,EAAE,WAAW,OAAO,KAC7C,IAAI,SAAS,WAAW,OAAO,GAAG;AAClC,UAAM,YAAYD,UAAQ,GAAG;AAC7B,QAAI,WAAW;AACX,aAAOD,WAAS,UAAU,KAAK,EAAE,GAAG,UAAU,YAAY,GAAG,WAAW,GAAG,EAAE,SAAS,UAAU,QAAQ,QAAQ,CAAC;AAAA,IACrH;AAAA,EACJ;AACA,SAAOA,WAAS,KAAK,YAAY,EAAE,QAAQ,CAAC;AAChD;;;AC1DA,IAAM,iBAAiB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACP,KAAK;AAAA,EACT;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,aAAW,OAAO,gBAAgB;AAC9B,QAAI,WAAW,GAAG,MAAM,QAAW;AAC/B,iBAAW,GAAG,IAAI,WAAW,GAAG,IAAI,IAAI;AAAA,IAC5C;AAAA,EACJ;AACA,MAAI,SAASF,sBAAoB,UAAU;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,aAAW,OAAO,gBAAgB;AAC9B,QAAI,WAAW,GAAG,MAAM,QAAW;AAC/B,iBAAW,GAAG,IAAI,eAAe,WAAW,GAAG,CAAC;AAAA,IACpD;AAAA,EACJ;AACA,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;AC/CnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,WAAW;AAAA,IACP,KAAK;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,MAAI,WAAW,aAAa,QAAW;AACnC,eAAW,WAAW,WAAW,WAAW,IAAI;AAAA,EACpD;AACA,MAAI,WAAW,SAAS,WAAW,QAAQ;AACvC,eAAW,MAAM;AAAA,EACrB;AACA,MAAI,SAASF,sBAAoB,UAAU;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,MAAI,WAAW,aAAa,QAAW;AACnC,eAAW,WAAW,eAAe,WAAW,QAAQ;AAAA,EAC5D;AACA,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;ACjCnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,EACb;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AACvD,QAAM,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,kBAAkB;AAC5D,MAAI,SAASF,sBAAoB,UAAU;AAC3C,MAAI,aAAa,IAAI,OAAO,IAAI,SAAS,CAAC;AAC1C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,MAAI,wBAAwB,GAAG,MAAM,WAAW;AAC5C,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAaF,mBAAiB,SAAS;AAE7C,SAAO,WAAW;AAClB,QAAM,YAAY,UAAU,aAAa,IAAI,KAAK,KAAK;AACvD,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK;AAAA,IACL;AAAA,IACA,SAAS;AAAA,MACL,SAAS,UAAU,aAAa,MAAM,SAAY,UAAU;AAAA,IAChE;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;ACnCnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,GAAG;AAAA,EACP;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AACvD,QAAM,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,IAAI,QAAQ,UAAU,SAAS,QAAQ;AACjF,MAAI,SAASF,sBAAoB,UAAU;AAC3C,MAAI,aAAa,OAAO,OAAO,IAAI,SAAS,CAAC;AAC7C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,KAAK,UAAU,CAAC,MAAM;AAC1C,MAAI,CAAC,CAAC,UAAU,QAAQ,EAAE,SAAS,wBAAwB,GAAG,KAAK,EAAE,GAAG;AACpE,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,YAAY,UAAU,aAAa,IAAI,KAAK,KAAK;AACvD,YAAU,aAAa,OAAO,KAAK;AACnC,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK;AAAA,IACL;AAAA,IACA,SAAS;AAAA,MACL,SAAS,QAAQ,WAAW,UAAU;AAAA,IAC1C;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;AClC5D,IAAMG,aAAW,CAAC,KAAK,YAAY,UAAU,CAAC,MAAMA,WAAe,KAAK,YAAY,EAAE,GAAG,SAAS,QAAQ,QAAQ,CAAC;AACnH,IAAMC,YAAU,CAAC,KAAK,YAAYA,UAAc,KAAK,OAAO;AAC5D,IAAMC,cAAY,yBAAyBD,WAASD,UAAQ;;;ACFnE,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,KAAK;AAAA,EACT;AACJ,CAAC;AACD,IAAM,OAAO;AACN,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,IAAI,IAAI,KAAK,IAAI;AAC7B,MAAI,SAASF,sBAAoB,UAAU;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,MAAI,kBAAkB,GAAG,MAAM,UAAU;AACrC,WAAO;AAAA,EACX;AACA,QAAM,YAAY,IAAI,IAAI,KAAK,IAAI;AACnC,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK,UAAU,SAAS;AAAA,IACxB;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;AC9BnE,IAAM,eAAe;AACrB,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,WAAW,IAAI,SAAS,QAAQ,cAAc,MAAM;AAE1D,MAAI,WAAW;AAEf,MAAI,SAASF,sBAAoB,UAAU;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,QAAQ,aAAa,KAAK,UAAU,QAAQ;AAClD,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,MAAI,OAAO;AACP,UAAM,CAAC,EAAE,EAAE,EAAE,OAAO,QAAQ,IAAI,IAAI;AACpC,QAAI,SAAS,UAAU,CAAC,WAAW,SAAS,CAAC,WAAW,QAAQ;AAC5D,iBAAW,QAAQ,SAAS,OAAO,EAAE;AACrC,iBAAW,SAAS,SAAS,QAAQ,EAAE;AAAA,IAC3C;AACA,QAAI,MAAM;AACN,iBAAW,SAAS;AAAA,IACxB;AAAA,EACJ;AACA,QAAM,WAAW,UAAU,SAAS,QAAQ,cAAc,MAAM;AAChE,YAAU,WAAW;AACrB,aAAW,OAAO,CAAC,SAAS,UAAU,QAAQ,aAAa,QAAQ,GAAG;AAClE,cAAU,aAAa,OAAO,GAAG;AAAA,EACrC;AACA,SAAO;AAAA,IACH,KAAK,UAAU,SAAS;AAAA,IACxB;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;ACvCnE,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,eAAe,CAAC,YAAY;AAC9B,MAAI,CAAC,SAAS;AACV,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,OAAO,YAAY,QAAQ,MAAM,GAAG,EAAE,IAAI,CAAC,WAAW;AACzD,QAAI,CAAC;AACD,aAAO,CAAC;AACZ,UAAM,CAAC,KAAK,KAAK,IAAI,OAAO,MAAM,GAAG;AACrC,WAAO,CAAC,KAAK,MAAM,QAAQ,KAAK,EAAE,CAAC;AAAA,EACvC,CAAC,CAAC;AACN;AACA,IAAM,kBAAkB,CAAC,YAAY;AACjC,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,SAAS,EAAE,GAAG;AAC1F,MAAI,YAAY,WAAW,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,SAAO,WAAW,YAAY,KAAK,GAAG,CAAC;AAC3C;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,QAAQ,UAAU,aAAa,uBAC/B,gBACA;AACN,QAAM,UAAU,MAAM,KAAK,UAAU,QAAQ;AAC7C,MAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,EAAE,IAAI,MAAM,OAAO,QAAQ,SAAS,OAAO,MAAM,IAAI,QAAQ;AACnE,QAAM,EAAE,QAAQ,GAAG,UAAU,IAAI,aAAa,WAAW,EAAE;AAE3D,MAAI,UAAU,aAAa,sBAAsB;AAC7C,cAAU,WAAW;AAAA,EACzB;AACA,QAAM,aAAa,OAAO,YAAY;AAAA,IAClC,CAAC,SAAS,OAAO,KAAK,KAAK,MAAS;AAAA,IACpC,CAAC,UAAU,OAAO,MAAM,KAAK,MAAS;AAAA,IACtC,CAAC,UAAU,MAAM;AAAA,IACjB,CAAC,QAAQ,IAAI;AAAA,IACb,CAAC,WAAW,SAAS;AAAA,IACrB,CAAC,SAAS,KAAK;AAAA,IACf,CAAC,SAAS,KAAK;AAAA,EACnB,EAAE,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,MAAS,CAAC;AAC7C,SAAO;AAAA,IACH,KAAK,GAAG,UAAU,MAAM,GAAG,EAAE;AAAA,IAC7B;AAAA,EACJ;AACJ;AACO,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,MAAM,UAAU,CAAC,GAAG,QAAQ,IAAI,QAAQ,GAAI,IAAI;AACvF,QAAM,OAAO,GAAG,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM;AAC/C,MAAI,QAAQ;AACR,YAAQ,SAAS;AAAA,EACrB;AACA,QAAM,QAAQ;AAAA,IACV,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB,OAAO;AAAA,EAC3B,EAAE,OAAO,OAAO;AAChB,MAAI,WAAW,MAAM,KAAK,GAAG;AAC7B,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMC,cAAY,yBAAyBF,WAASC,UAAQ;;;ACrEnE,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,cAAc,CAAC,QAAQ,IAAI,SAAS,WAAW,iBAAiB;AACtE,IAAM,EAAE,qBAAAE,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB,CAAC,CAAC;AACtE,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,WAAW,IAAI,SAAS,QAAQ,mBAAmB,kBAAkB;AAE3E,MAAI,WAAW;AAEf,MAAI,WAAW,UAAU,WAAW,WAAW,UAAU;AACrD,WAAO,WAAW;AAAA,EACtB;AAEA,MAAI,SAASF,sBAAoB,UAAU;AAE3C,SAAO,qBAAqB,GAAG,EAAE,QAAQ,oBAAoB,iBAAiB;AAClF;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,YAAY,UAAU,SAAS,QAAQ,mBAAmB,EAAE,EAAE,QAAQ,oBAAoB,EAAE;AAClG,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,MACH,KAAK,qBAAqB,SAAS;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,KAAK,GAAG,UAAU,MAAM,GAAG,kBAAkB,GAAG,SAAS;AAAA,IACzD;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;ACjCnE,IAAM,kBAAkB;AACxB,IAAM,EAAE,qBAAAG,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,QAAQ;AAAA,EACZ;AAAA,EACA,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AACM,IAAMC,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,QAAQ,gBAAgB,KAAK,UAAU,SAAS,CAAC;AACvD,MAAI,CAAC,SAAS,CAAC,MAAM,QAAQ;AACzB,WAAO;AAAA,EACX;AACA,QAAM,EAAE,MAAM,KAAK,IAAI,MAAM;AAC7B,QAAM,CAAC,EAAE,GAAG,gBAAgB,IAAI,UAAU,SAAS,MAAM,KAAK;AAC9D,QAAM,aAAaD,mBAAiB,iBAAiB,KAAK,KAAK,KAAK,EAAE;AACtE,MAAI,WAAW,QAAQ;AACnB,UAAM,CAAC,OAAO,MAAM,IAAI,WAAW,OAAO,MAAM,GAAG;AACnD,QAAI;AACA,iBAAW,QAAQ,SAAS,KAAK;AACrC,QAAI;AACA,iBAAW,SAAS,SAAS,MAAM;AACvC,WAAO,WAAW;AAAA,EACtB;AACA,SAAO;AAAA,IACH,KAAK,WAAW,IAAI,IAAI,IAAI;AAAA,IAC5B;AAAA,IACA,SAAS,EAAE,KAAK;AAAA,EACpB;AACJ;AACO,IAAME,aAAW,CAAC,KAAK,YAAY,UAAU,CAAC,MAAM;AACvD,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,OAAO,QAAQ,QAAQ,IAAI;AAEjC,QAAM,QAAQ,gBAAgB,KAAK,IAAI,SAAS,CAAC;AACjD,MAAI,OAAO,QAAQ;AACf,QAAI,WAAW,IAAI,MAAM,OAAO,IAAI;AAAA,EACxC;AACA,aAAW,SAAS,WAAW,UAC3B,GAAG,WAAW,SAAS,EAAE,IAAI,WAAW,UAAU,EAAE;AACxD,SAAO,WAAW;AAClB,SAAO,WAAW;AAClB,QAAM,YAAY,iBAAiBH,sBAAoB,UAAU,CAAC;AAClE,MAAI,WAAW;AACf,MAAI,WAAW,mBAAmB,IAAI,QAAQ,KACzC,YAAY,MAAM,SAAS,KAAK,OAAO,OAAO,QAAQ,YAAY;AACvE,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMI,cAAY,yBAAyBF,WAASC,UAAQ;;;ACtDnE,IAAM,EAAE,qBAAAE,uBAAqB,kBAAAC,mBAAiB,IAAI,yBAAyB;AAAA,EACvE,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,UAAU;AAAA,IACN,MAAM;AAAA,EACV;AACJ,CAAC;AACM,IAAMC,aAAW,CAAC,KAAK,eAAe;AACzC,QAAM,MAAM,MAAM,GAAG;AACrB,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,OAAO,SAAS,eAAe,SAAS,KAAK;AAC7C,eAAW,OAAO,OAAO,MAAM;AAAA,EACnC;AACA,MAAI,SAASF,sBAAoB,UAAU;AAC3C,SAAO,qBAAqB,GAAG;AACnC;AACO,IAAMG,YAAU,CAAC,QAAQ;AAC5B,QAAM,YAAY,MAAM,GAAG;AAC3B,QAAM,aAAaF,mBAAiB,SAAS;AAC7C,MAAI,WAAW,SAAS,QAAW;AAC/B,eAAW,OAAO,WAAW,SAAS;AAAA,EAC1C;AACA,YAAU,SAAS;AACnB,SAAO;AAAA,IACH,KAAK,qBAAqB,SAAS;AAAA,IACnC;AAAA,EACJ;AACJ;AACO,IAAMG,cAAY,yBAAyBD,WAASD,UAAQ;;;ACHnE,IAAM,iBAAiB;AAAA,EACnB;AAAA,EACA,OAAAG;AAAA,EACA,cAAcA;AAAA,EACd,OAAAA;AAAA,EACA,YAAAA;AAAA,EACA,mBAAAA;AAAA,EACA,YAAAA;AAAA,EACA,YAAAA;AAAA,EACA,YAAAA;AAAA,EACA,cAAAA;AAAA,EACA,UAAAA;AAAA,EACA,SAAAA;AAAA,EACA,aAAAA;AAAA,EACA,UAAAA;AAAA,EACA,OAAAA;AAAA,EACA,KAAAA;AAAA,EACA,QAAAA;AAAA,EACA,cAAcA;AAAA,EACd,SAAAA;AAAA,EACA,QAAAA;AAAA,EACA,QAAAA;AAAA,EACA,SAAAA;AAAA,EACA,WAAAA;AAAA,EACA,UAAAA;AAAA,EACA,YAAAA;AAAA,EACA,QAAAA;AAAA,EACA,WAAAA;AACJ;AAIO,SAAS,qBAAqB,KAAK;AACtC,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AACA,SAAO,eAAe,GAAG;AAC7B;;;AC/CA,SAAS,eAAe;AAAA,EACtB;AAAA,EACA;AAAA,EACA,aAAa,CAAC;AAAA,EACd;AAAA,EACA,GAAG;AACL,GAAG;AACD,UAAQ,kBAAkB,MAAM,GAAG,KAAK;AACxC,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,QAAM,cAAc,qBAAqB,GAAG;AAC5C,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,SAAO,wBAAwB;AAAA,IAC7B,GAAG;AAAA,IACH,YAAY,aAAa,GAAG;AAAA,IAC5B,SAAS,UAAU,GAAG;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AACA,SAAS,qBAAqB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAAG;AACD,UAAQ,kBAAkB,MAAM,GAAG,KAAK;AACxC,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,QAAM,cAAc,qBAAqB,GAAG;AAC5C,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AACA,SAAO,yBAAyB;AAAA,IAC9B,GAAG;AAAA,IACH,YAAY,aAAa,GAAG;AAAA,IAC5B,SAAS,UAAU,GAAG;AAAA,IACtB;AAAA,EACF,CAAC;AACH;;;AnCtDA,yBAAoB;AAWpB,IAAAC,UAAwB;AAExB,IAAAC,sBAA4B;AAZ5B,IAAI,QAAc;AAAA,EAChB,SAAS,OAAO,OAAO,KAAK;AAC1B,UAAM,iBAAiB;AAAA,MACrB,eAAe,KAAK;AAAA,IACtB;AACA,eAAuB,wBAAI,OAAO,EAAE,GAAG,gBAAgB,IAAI,CAAC;AAAA,EAC9D;AACF;AAMA,IAAI,SAAgB;AAAA,EAClB,SAAS,QAAQ,OAAO,KAAK;AAC3B,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE;AAAA,MACF;AAAA,IACF;AACA,eAAuB,oBAAAC,KAAK,UAAU,EAAE,GAAG,gBAAgB,IAAI,CAAC;AAAA,EAClE;AACF;", "names": ["React", "transform", "operationsGenerator", "operationsParser", "extract", "generate", "operationsParser", "operationsGenerator", "generate", "extract", "transform", "operationsGenerator", "extract", "generate", "transform", "operationsGenerator", "extract", "generate", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "transform", "operationsParser", "operationsGenerator", "operationsGenerator", "generate", "extract", "extractAndGenerate", "transform", "operationsGenerator", "generate", "extract", "transform", "operationsGenerator", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "extract", "generate", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "extract", "generate", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "extract", "generate", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "operationsGenerator", "operationsParser", "extract", "generate", "transform", "operationsGenerator", "operationsParser", "generate", "extract", "transform", "transform", "React2", "import_jsx_runtime", "jsx2"]}