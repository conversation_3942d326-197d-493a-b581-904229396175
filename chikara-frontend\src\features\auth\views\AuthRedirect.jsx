import { useAuthStore, useSocketStore } from "@/app/store/stores";
import { handleLogout } from "@/helpers/handleLogout";
import useGameConfig from "@/hooks/useGameConfig";
import posthog from "posthog-js";
import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";

const AUTH_PROVIDERS = {
    CREDENTIALS: "credentials",
    DISCORD: "discord",
    GOOGLE: "google",
};

const AuthRedirect = () => {
    const navigate = useNavigate();
    const { data, error, isLoading } = useFetchCurrentUser();
    const setAuthed = useAuthStore((state) => state.setAuthed);

    const { fetchSocket } = useSocketStore();
    const { LOGIN_DISABLED } = useGameConfig("authConfig");
    const [searchParams] = useSearchParams();
    const auth = searchParams.get("auth");
    const email = data?.user?.email;

    useEffect(() => {
        if (isLoading) return;

        if (LOGIN_DISABLED || !auth || !Object.values(AUTH_PROVIDERS).includes(auth)) {
            handleLogout();
            return;
        }

        if (data && !error) {
            setAuthed(true);
            fetchSocket();

            if (auth && email) {
                posthog.capture(`${auth}_login`, { email });
            }
            navigate("/home");
        }

        if (error) {
            console.error(`${auth && auth} Authentication failed: ${error.message}`);
            handleLogout();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoading, error, data, navigate, LOGIN_DISABLED, auth]);

    return null;
};

export default AuthRedirect;
